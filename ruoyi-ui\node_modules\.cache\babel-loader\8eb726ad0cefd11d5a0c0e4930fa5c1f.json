{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\alertEvent.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\views\\frailty\\event\\alertEvent.vue", "mtime": 1756199950483}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751956538244}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi93c2gvYXVnbWVudF93b3Jrc3BhY2UvYXFzb2MtbWFpbi9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKdmFyIF9vYmplY3RTcHJlYWQyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJFOi93c2gvYXVnbWVudF93b3Jrc3BhY2UvYXFzb2MtbWFpbi9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9vYmplY3RTcHJlYWQyLmpzIikpOwp2YXIgX2V2ZW50TGlzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi9jb21wb25lbnQvZXZlbnRMaXN0IikpOwp2YXIgX2F0dGFja1ZpZXdMaXN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIuL2NvbXBvbmVudC9hdHRhY2tWaWV3TGlzdCIpKTsKdmFyIF9zdWZmZXJWaWV3TGlzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi9jb21wb25lbnQvc3VmZmVyVmlld0xpc3QiKSk7CnZhciBfaW5kZXggPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvdmlld3MvdGhyZWF0L2Fzc2V0L2luZGV4IikpOwp2YXIgX2luZGV4MiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC92aWV3cy9hcXNvYy9mZnNhZmUtaXBmaWx0ZXItbG9nL2luZGV4IikpOwp2YXIgX2hpc3RvcnkgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvdmlld3MvYXFzb2MvZmZzYWZlLWlwZmlsdGVyLWxvZy9oaXN0b3J5IikpOwp2YXIgX3RocmVhdCA9IHJlcXVpcmUoIkAvYXBpL3RocmVhdC90aHJlYXQiKTsKdmFyIF9BdHRhY2tBbGFybSA9IHJlcXVpcmUoIkAvYXBpL3RocmVhdGVuL0F0dGFja0FsYXJtIik7CnZhciBfaG9uZXlwb3RBbGFybUxpc3QgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIi4vY29tcG9uZW50L2hvbmV5cG90QWxhcm1MaXN0IikpOwp2YXIgX2hvbmV5cG90QXR0YWNrVmlld0xpc3QgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIi4vY29tcG9uZW50L2hvbmV5cG90QXR0YWNrVmlld0xpc3QiKSk7CnZhciBfYXBpQWxhcm1MaXN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIuL2NvbXBvbmVudC9hcGlBbGFybUxpc3QiKSk7CnZhciBfZmZzYWZlSXBGaWx0ZXJibG9ja2luZyA9IHJlcXVpcmUoIkAvYXBpL3NhZmUvZmZzYWZlSXBGaWx0ZXJibG9ja2luZyIpOwp2YXIgX2Zsb3dSaXNrQXNzZXRzID0gcmVxdWlyZSgiQC9hcGkvZmZzYWZlL2Zsb3dSaXNrQXNzZXRzIik7CnZhciBfcnVveWkgPSByZXF1aXJlKCJAL3V0aWxzL3J1b3lpIik7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAnQWxlcnRFdmVudCcsCiAgY29tcG9uZW50czogewogICAgU3VmZmVyVmlld0xpc3Q6IF9zdWZmZXJWaWV3TGlzdC5kZWZhdWx0LAogICAgQXR0YWNrVmlld0xpc3Q6IF9hdHRhY2tWaWV3TGlzdC5kZWZhdWx0LAogICAgRXZlbnRMaXN0OiBfZXZlbnRMaXN0LmRlZmF1bHQsCiAgICBBc3NldFZpZXc6IF9pbmRleC5kZWZhdWx0LAogICAgSXBmaWx0ZXJMb2c6IF9pbmRleDIuZGVmYXVsdCwKICAgIElwRmlsdGVyTG9nSGlzdG9yeTogX2hpc3RvcnkuZGVmYXVsdCwKICAgIEhvbmV5cG90QWxhcm1MaXN0OiBfaG9uZXlwb3RBbGFybUxpc3QuZGVmYXVsdCwKICAgIEhvbmV5cG90QXR0YWNrVmlld0xpc3Q6IF9ob25leXBvdEF0dGFja1ZpZXdMaXN0LmRlZmF1bHQsCiAgICBBcGlBbGFybUxpc3Q6IF9hcGlBbGFybUxpc3QuZGVmYXVsdAogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICByZXR1cm4gewogICAgICBhY3RpdmVOYW1lOiAnZmlyc3QnLAogICAgICBwcm9wQWN0aXZlTmFtZTogJ2ZpcnN0JywKICAgICAgc3JjUXVlcnlQYXJhbXM6IHt9LAogICAgICBxdWVyeVBhcmFtczoge30sCiAgICAgIGN1cnJlbnRRdWVyeVBhcmFtczoge30sCiAgICAgIGN1cnJlbnRDYXJkOiAxLAogICAgICBjdXJyZW50QnRuOiBudWxsLAogICAgICBoZWFkQ2FyZE9wdGlvbnM6IFt7CiAgICAgICAgdGl0bGU6ICfmtYHph4/lqIHog4HlkYroraYnLAogICAgICAgIHRvdGFsOiBmdW5jdGlvbiB0b3RhbCgpIHsKICAgICAgICAgIHJldHVybiBfdGhpcy5nZXRTdGF0aXN0aWNzVmFsdWUoX3RoaXMuZ2V0R3JvdXBTdGF0aXN0aWNzRGF0YSwgJ3RvdGFsJyk7CiAgICAgICAgfSwKICAgICAgICBrZXk6IDEsCiAgICAgICAgY2xpY2s6IGZ1bmN0aW9uIGNsaWNrKCkgewogICAgICAgICAgX3RoaXMuaGVhZEJ0bkNsaWNrKDEsIG51bGwpOwogICAgICAgICAgX3RoaXMuY3VycmVudFF1ZXJ5UGFyYW1zLmFsYXJtTGV2ZWwgPSBudWxsOwogICAgICAgICAgX3RoaXMuY3VycmVudFF1ZXJ5UGFyYW1zLnJpc2tMZXZlbCA9IG51bGw7CiAgICAgICAgICBfdGhpcy5xdWVyeVBhcmFtcyA9ICgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSgoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoe30sIF90aGlzLnNyY1F1ZXJ5UGFyYW1zKSwgX3RoaXMuY3VycmVudFF1ZXJ5UGFyYW1zKTsKICAgICAgICB9LAogICAgICAgIGJ0bkFycjogWwogICAgICAgIC8qewogICAgICAgICAgaWNvbjogcmVxdWlyZSgnQC9hc3NldHMvaWNvbnMvZXZlbnQvbGV2ZWw1LnBuZycpLAogICAgICAgICAgdmFsdWU6ICgpID0+IHRoaXMuZ2V0U3RhdGlzdGljc1ZhbHVlKHRoaXMuZ2V0R3JvdXBTdGF0aXN0aWNzRGF0YSwgJ2FsYXJtTGV2ZWw1JyksCiAgICAgICAgICBsYWJlbDogJ+S4pemHjScsCiAgICAgICAgICBrZXk6IDUsCiAgICAgICAgICBjbGljazogKCkgPT4gewogICAgICAgICAgICB0aGlzLmhlYWRCdG5DbGljaygxLCA1KQogICAgICAgICAgICB0aGlzLmN1cnJlbnRRdWVyeVBhcmFtcy5hbGFybUxldmVsID0gJzUnCiAgICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMgPSB7IC4uLnRoaXMuc3JjUXVlcnlQYXJhbXMsIC4uLnRoaXMuY3VycmVudFF1ZXJ5UGFyYW1zIH0KICAgICAgICAgIH0KICAgICAgICB9LCovCiAgICAgICAgewogICAgICAgICAgaWNvbjogcmVxdWlyZSgnQC9hc3NldHMvaWNvbnMvZXZlbnQvbGV2ZWw0LnBuZycpLAogICAgICAgICAgdmFsdWU6IGZ1bmN0aW9uIHZhbHVlKCkgewogICAgICAgICAgICByZXR1cm4gX3RoaXMuZ2V0U3RhdGlzdGljc1ZhbHVlKF90aGlzLmdldEdyb3VwU3RhdGlzdGljc0RhdGEsICdhbGFybUxldmVsNCcpOwogICAgICAgICAgfSwKICAgICAgICAgIGxhYmVsOiAn6auY5Y2xJywKICAgICAgICAgIGtleTogNCwKICAgICAgICAgIGNsaWNrOiBmdW5jdGlvbiBjbGljaygpIHsKICAgICAgICAgICAgX3RoaXMuaGVhZEJ0bkNsaWNrKDEsIDQpOwogICAgICAgICAgICBfdGhpcy5jdXJyZW50UXVlcnlQYXJhbXMuYWxhcm1MZXZlbCA9ICc0JzsKICAgICAgICAgICAgX3RoaXMucXVlcnlQYXJhbXMgPSAoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCBfdGhpcy5zcmNRdWVyeVBhcmFtcyksIF90aGlzLmN1cnJlbnRRdWVyeVBhcmFtcyk7CiAgICAgICAgICB9CiAgICAgICAgfSwgewogICAgICAgICAgaWNvbjogcmVxdWlyZSgnQC9hc3NldHMvaWNvbnMvZXZlbnQvbGV2ZWwzLnBuZycpLAogICAgICAgICAgdmFsdWU6IGZ1bmN0aW9uIHZhbHVlKCkgewogICAgICAgICAgICByZXR1cm4gX3RoaXMuZ2V0U3RhdGlzdGljc1ZhbHVlKF90aGlzLmdldEdyb3VwU3RhdGlzdGljc0RhdGEsICdhbGFybUxldmVsMycpOwogICAgICAgICAgfSwKICAgICAgICAgIGxhYmVsOiAn5Lit5Y2xJywKICAgICAgICAgIGtleTogMywKICAgICAgICAgIGNsaWNrOiBmdW5jdGlvbiBjbGljaygpIHsKICAgICAgICAgICAgX3RoaXMuaGVhZEJ0bkNsaWNrKDEsIDMpOwogICAgICAgICAgICBfdGhpcy5jdXJyZW50UXVlcnlQYXJhbXMuYWxhcm1MZXZlbCA9ICczJzsKICAgICAgICAgICAgX3RoaXMucXVlcnlQYXJhbXMgPSAoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCBfdGhpcy5zcmNRdWVyeVBhcmFtcyksIF90aGlzLmN1cnJlbnRRdWVyeVBhcmFtcyk7CiAgICAgICAgICB9CiAgICAgICAgfSwgewogICAgICAgICAgaWNvbjogcmVxdWlyZSgnQC9hc3NldHMvaWNvbnMvZXZlbnQvbGV2ZWwyLnBuZycpLAogICAgICAgICAgdmFsdWU6IGZ1bmN0aW9uIHZhbHVlKCkgewogICAgICAgICAgICByZXR1cm4gX3RoaXMuZ2V0U3RhdGlzdGljc1ZhbHVlKF90aGlzLmdldEdyb3VwU3RhdGlzdGljc0RhdGEsICdhbGFybUxldmVsMicpOwogICAgICAgICAgfSwKICAgICAgICAgIGxhYmVsOiAn5L2O5Y2xJywKICAgICAgICAgIGtleTogMiwKICAgICAgICAgIGNsaWNrOiBmdW5jdGlvbiBjbGljaygpIHsKICAgICAgICAgICAgX3RoaXMuaGVhZEJ0bkNsaWNrKDEsIDIpOwogICAgICAgICAgICBfdGhpcy5jdXJyZW50UXVlcnlQYXJhbXMuYWxhcm1MZXZlbCA9ICcyJzsKICAgICAgICAgICAgX3RoaXMucXVlcnlQYXJhbXMgPSAoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCBfdGhpcy5zcmNRdWVyeVBhcmFtcyksIF90aGlzLmN1cnJlbnRRdWVyeVBhcmFtcyk7CiAgICAgICAgICB9CiAgICAgICAgfV0KICAgICAgfSwgewogICAgICAgIHRpdGxlOiAn6Jyc572Q6K+x5o2V5ZGK6K2mJywKICAgICAgICB0b3RhbDogZnVuY3Rpb24gdG90YWwoKSB7CiAgICAgICAgICByZXR1cm4gX3RoaXMuZ2V0U3RhdGlzdGljc1ZhbHVlKF90aGlzLmhvbmV5cG90QWxhcm1TdGF0aXN0aWNzRGF0YSwgJ3RvdGFsJyk7CiAgICAgICAgfSwKICAgICAgICBrZXk6IDIsCiAgICAgICAgY2xpY2s6IGZ1bmN0aW9uIGNsaWNrKCkgewogICAgICAgICAgcmV0dXJuIF90aGlzLmhlYWRCdG5DbGljaygyLCBudWxsKTsKICAgICAgICB9LAogICAgICAgIGJ0bkFycjogW3sKICAgICAgICAgIGljb246IHJlcXVpcmUoJ0AvYXNzZXRzL2ljb25zL2V2ZW50L2xldmVsNS5wbmcnKSwKICAgICAgICAgIHZhbHVlOiBmdW5jdGlvbiB2YWx1ZSgpIHsKICAgICAgICAgICAgcmV0dXJuIF90aGlzLmdldFN0YXRpc3RpY3NWYWx1ZShfdGhpcy5ob25leXBvdEFsYXJtU3RhdGlzdGljc0RhdGEsICdhbGFybUxldmVsNScpOwogICAgICAgICAgfSwKICAgICAgICAgIGxhYmVsOiAn5Lil6YeNJywKICAgICAgICAgIGtleTogNSwKICAgICAgICAgIGNsaWNrOiBmdW5jdGlvbiBjbGljaygpIHsKICAgICAgICAgICAgX3RoaXMuaGVhZEJ0bkNsaWNrKDIsIDUpOwogICAgICAgICAgICBfdGhpcy5jdXJyZW50UXVlcnlQYXJhbXMuYWxhcm1MZXZlbCA9ICc1JzsKICAgICAgICAgICAgX3RoaXMucXVlcnlQYXJhbXMgPSAoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCBfdGhpcy5zcmNRdWVyeVBhcmFtcyksIF90aGlzLmN1cnJlbnRRdWVyeVBhcmFtcyk7CiAgICAgICAgICB9CiAgICAgICAgfSwgewogICAgICAgICAgaWNvbjogcmVxdWlyZSgnQC9hc3NldHMvaWNvbnMvZXZlbnQvbGV2ZWw0LnBuZycpLAogICAgICAgICAgdmFsdWU6IGZ1bmN0aW9uIHZhbHVlKCkgewogICAgICAgICAgICByZXR1cm4gX3RoaXMuZ2V0U3RhdGlzdGljc1ZhbHVlKF90aGlzLmhvbmV5cG90QWxhcm1TdGF0aXN0aWNzRGF0YSwgJ2FsYXJtTGV2ZWw0Jyk7CiAgICAgICAgICB9LAogICAgICAgICAgbGFiZWw6ICfpq5jljbEnLAogICAgICAgICAga2V5OiA0LAogICAgICAgICAgY2xpY2s6IGZ1bmN0aW9uIGNsaWNrKCkgewogICAgICAgICAgICBfdGhpcy5oZWFkQnRuQ2xpY2soMiwgNCk7CiAgICAgICAgICAgIF90aGlzLmN1cnJlbnRRdWVyeVBhcmFtcy5hbGFybUxldmVsID0gJzQnOwogICAgICAgICAgICBfdGhpcy5xdWVyeVBhcmFtcyA9ICgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSgoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoe30sIF90aGlzLnNyY1F1ZXJ5UGFyYW1zKSwgX3RoaXMuY3VycmVudFF1ZXJ5UGFyYW1zKTsKICAgICAgICAgIH0KICAgICAgICB9LCB7CiAgICAgICAgICBpY29uOiByZXF1aXJlKCdAL2Fzc2V0cy9pY29ucy9ldmVudC9sZXZlbDMucG5nJyksCiAgICAgICAgICB2YWx1ZTogZnVuY3Rpb24gdmFsdWUoKSB7CiAgICAgICAgICAgIHJldHVybiBfdGhpcy5nZXRTdGF0aXN0aWNzVmFsdWUoX3RoaXMuaG9uZXlwb3RBbGFybVN0YXRpc3RpY3NEYXRhLCAnYWxhcm1MZXZlbDMnKTsKICAgICAgICAgIH0sCiAgICAgICAgICBsYWJlbDogJ+S4reWNsScsCiAgICAgICAgICBrZXk6IDMsCiAgICAgICAgICBjbGljazogZnVuY3Rpb24gY2xpY2soKSB7CiAgICAgICAgICAgIF90aGlzLmhlYWRCdG5DbGljaygyLCAzKTsKICAgICAgICAgICAgX3RoaXMuY3VycmVudFF1ZXJ5UGFyYW1zLmFsYXJtTGV2ZWwgPSAnMyc7CiAgICAgICAgICAgIF90aGlzLnF1ZXJ5UGFyYW1zID0gKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKCgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSh7fSwgX3RoaXMuc3JjUXVlcnlQYXJhbXMpLCBfdGhpcy5jdXJyZW50UXVlcnlQYXJhbXMpOwogICAgICAgICAgfQogICAgICAgIH0sIHsKICAgICAgICAgIGljb246IHJlcXVpcmUoJ0AvYXNzZXRzL2ljb25zL2V2ZW50L2xldmVsMi5wbmcnKSwKICAgICAgICAgIHZhbHVlOiBmdW5jdGlvbiB2YWx1ZSgpIHsKICAgICAgICAgICAgcmV0dXJuIF90aGlzLmdldFN0YXRpc3RpY3NWYWx1ZShfdGhpcy5ob25leXBvdEFsYXJtU3RhdGlzdGljc0RhdGEsICdhbGFybUxldmVsMicpOwogICAgICAgICAgfSwKICAgICAgICAgIGxhYmVsOiAn5L2O5Y2xJywKICAgICAgICAgIGtleTogMiwKICAgICAgICAgIGNsaWNrOiBmdW5jdGlvbiBjbGljaygpIHsKICAgICAgICAgICAgX3RoaXMuaGVhZEJ0bkNsaWNrKDIsIDIpOwogICAgICAgICAgICBfdGhpcy5jdXJyZW50UXVlcnlQYXJhbXMuYWxhcm1MZXZlbCA9ICcyJzsKICAgICAgICAgICAgX3RoaXMucXVlcnlQYXJhbXMgPSAoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCBfdGhpcy5zcmNRdWVyeVBhcmFtcyksIF90aGlzLmN1cnJlbnRRdWVyeVBhcmFtcyk7CiAgICAgICAgICB9CiAgICAgICAgfV0KICAgICAgfSwgewogICAgICAgIHRpdGxlOiAnQVBJ5ZGK6K2mJywKICAgICAgICB0b3RhbDogZnVuY3Rpb24gdG90YWwoKSB7CiAgICAgICAgICByZXR1cm4gX3RoaXMuZ2V0U3RhdGlzdGljc1ZhbHVlKF90aGlzLmFwaUFsYXJtU3RhdGlzdGljc0RhdGEsICd0b3RhbCcpOwogICAgICAgIH0sCiAgICAgICAga2V5OiAzLAogICAgICAgIGNsaWNrOiBmdW5jdGlvbiBjbGljaygpIHsKICAgICAgICAgIF90aGlzLmhlYWRCdG5DbGljaygzLCBudWxsKTsKICAgICAgICAgIF90aGlzLmN1cnJlbnRRdWVyeVBhcmFtcy5yaXNrVHlwZSA9IG51bGw7CiAgICAgICAgICBfdGhpcy5xdWVyeVBhcmFtcyA9ICgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSgoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoe30sIF90aGlzLnNyY1F1ZXJ5UGFyYW1zKSwgX3RoaXMuY3VycmVudFF1ZXJ5UGFyYW1zKTsKICAgICAgICB9LAogICAgICAgIGJ0bkFycjogW3sKICAgICAgICAgIGljb246IHJlcXVpcmUoJ0AvYXNzZXRzL2ljb25zL2V2ZW50L3dlYWtQYXNzd29yZC5wbmcnKSwKICAgICAgICAgIHZhbHVlOiBmdW5jdGlvbiB2YWx1ZSgpIHsKICAgICAgICAgICAgcmV0dXJuIF90aGlzLmdldFN0YXRpc3RpY3NWYWx1ZShfdGhpcy5hcGlBbGFybVN0YXRpc3RpY3NEYXRhLCAnd2Vha1Bhc3N3b3JkJyk7CiAgICAgICAgICB9LAogICAgICAgICAgbGFiZWw6ICflvLHlj6Pku6TotKblj7cnLAogICAgICAgICAga2V5OiAxLAogICAgICAgICAgY2xpY2s6IGZ1bmN0aW9uIGNsaWNrKCkgewogICAgICAgICAgICBfdGhpcy5oZWFkQnRuQ2xpY2soMywgMSk7CiAgICAgICAgICAgIF90aGlzLmN1cnJlbnRRdWVyeVBhcmFtcy5yaXNrVHlwZSA9ICd3ZWFrX3Bhc3N3b3JkJzsKICAgICAgICAgICAgX3RoaXMucXVlcnlQYXJhbXMgPSAoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCBfdGhpcy5zcmNRdWVyeVBhcmFtcyksIF90aGlzLmN1cnJlbnRRdWVyeVBhcmFtcyk7CiAgICAgICAgICB9CiAgICAgICAgfSwgewogICAgICAgICAgaWNvbjogcmVxdWlyZSgnQC9hc3NldHMvaWNvbnMvZXZlbnQv5pWP5oSf5L+h5oGvLnBuZycpLAogICAgICAgICAgdmFsdWU6IGZ1bmN0aW9uIHZhbHVlKCkgewogICAgICAgICAgICByZXR1cm4gX3RoaXMuZ2V0U3RhdGlzdGljc1ZhbHVlKF90aGlzLmFwaUFsYXJtU3RhdGlzdGljc0RhdGEsICdzZW5zaXRpdmVJbmZvJyk7CiAgICAgICAgICB9LAogICAgICAgICAgbGFiZWw6ICfmlY/mhJ/kv6Hmga8nLAogICAgICAgICAga2V5OiAyLAogICAgICAgICAgY2xpY2s6IGZ1bmN0aW9uIGNsaWNrKCkgewogICAgICAgICAgICBfdGhpcy5oZWFkQnRuQ2xpY2soMywgMik7CiAgICAgICAgICAgIF90aGlzLmN1cnJlbnRRdWVyeVBhcmFtcy5yaXNrVHlwZSA9ICdzZW5zaXRpdmVfaW5mbyc7CiAgICAgICAgICAgIF90aGlzLnF1ZXJ5UGFyYW1zID0gKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKCgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSh7fSwgX3RoaXMuc3JjUXVlcnlQYXJhbXMpLCBfdGhpcy5jdXJyZW50UXVlcnlQYXJhbXMpOwogICAgICAgICAgfQogICAgICAgIH0sIHsKICAgICAgICAgIGljb246IHJlcXVpcmUoJ0AvYXNzZXRzL2ljb25zL2V2ZW50L+mrmOWNsei1hOS6py5wbmcnKSwKICAgICAgICAgIHZhbHVlOiBmdW5jdGlvbiB2YWx1ZSgpIHsKICAgICAgICAgICAgcmV0dXJuIF90aGlzLmdldFN0YXRpc3RpY3NWYWx1ZShfdGhpcy5hcGlBbGFybVN0YXRpc3RpY3NEYXRhLCAnaGlnaFJpc2tBc3NldHMnKTsKICAgICAgICAgIH0sCiAgICAgICAgICBsYWJlbDogJ+mrmOWNsei1hOS6pycsCiAgICAgICAgICBrZXk6IDMsCiAgICAgICAgICBjbGljazogZnVuY3Rpb24gY2xpY2soKSB7CiAgICAgICAgICAgIF90aGlzLmhlYWRCdG5DbGljaygzLCAzKTsKICAgICAgICAgICAgX3RoaXMuY3VycmVudFF1ZXJ5UGFyYW1zLnJpc2tUeXBlID0gJ2hpZ2hfcmlza19hc3NldHMnOwogICAgICAgICAgICBfdGhpcy5xdWVyeVBhcmFtcyA9ICgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSgoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoe30sIF90aGlzLnNyY1F1ZXJ5UGFyYW1zKSwgX3RoaXMuY3VycmVudFF1ZXJ5UGFyYW1zKTsKICAgICAgICAgIH0KICAgICAgICB9XQogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICflrp7ml7bpmLvmlq0nLAogICAgICAgIHRvdGFsOiBmdW5jdGlvbiB0b3RhbCgpIHsKICAgICAgICAgIHJldHVybiBfdGhpcy5nZXRTdGF0aXN0aWNzVmFsdWUoX3RoaXMuZmlsdGVyTG9nU3RhdGlzdGljRGF0YSwgJ3RvdGFsJyk7CiAgICAgICAgfSwKICAgICAgICBrZXk6IDQsCiAgICAgICAgY2xpY2s6IGZ1bmN0aW9uIGNsaWNrKCkgewogICAgICAgICAgX3RoaXMuaGVhZEJ0bkNsaWNrKDQsIG51bGwpOwogICAgICAgICAgX3RoaXMucXVlcnlQYXJhbXMgPSAoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCBfdGhpcy5zcmNRdWVyeVBhcmFtcyksIF90aGlzLmN1cnJlbnRRdWVyeVBhcmFtcyk7CiAgICAgICAgfSwKICAgICAgICBidG5BcnI6IFt7CiAgICAgICAgICBpY29uOiByZXF1aXJlKCdAL2Fzc2V0cy9pY29ucy9ldmVudC/mraPlnKjpmLvmlq0ucG5nJyksCiAgICAgICAgICB2YWx1ZTogZnVuY3Rpb24gdmFsdWUoKSB7CiAgICAgICAgICAgIHJldHVybiBfdGhpcy5nZXRTdGF0aXN0aWNzVmFsdWUoX3RoaXMuZmlsdGVyTG9nU3RhdGlzdGljRGF0YSwgJ2Jsb2NraW5nQ291bnQnKTsKICAgICAgICAgIH0sCiAgICAgICAgICBsYWJlbDogJ+ato+WcqOmYu+aWrScsCiAgICAgICAgICBrZXk6IDEsCiAgICAgICAgICBjbGljazogZnVuY3Rpb24gY2xpY2soKSB7CiAgICAgICAgICAgIF90aGlzLnByb3BBY3RpdmVOYW1lID0gJ2ZpcnN0JzsKICAgICAgICAgICAgX3RoaXMuYWN0aXZlTmFtZSA9ICdmaXJzdCc7CiAgICAgICAgICAgIF90aGlzLmhlYWRCdG5DbGljayg0LCAxKTsKICAgICAgICAgIH0KICAgICAgICB9LCB7CiAgICAgICAgICBpY29uOiByZXF1aXJlKCdAL2Fzc2V0cy9pY29ucy9ldmVudC/pmLvmlq3ljoblj7IucG5nJyksCiAgICAgICAgICB2YWx1ZTogZnVuY3Rpb24gdmFsdWUoKSB7CiAgICAgICAgICAgIHJldHVybiBfdGhpcy5nZXRTdGF0aXN0aWNzVmFsdWUoX3RoaXMuZmlsdGVyTG9nU3RhdGlzdGljRGF0YSwgJ2Jsb2NrTG9nQ291bnQnKTsKICAgICAgICAgIH0sCiAgICAgICAgICBsYWJlbDogJ+WOhuWPsuiusOW9lScsCiAgICAgICAgICBrZXk6IDIsCiAgICAgICAgICBjbGljazogZnVuY3Rpb24gY2xpY2soKSB7CiAgICAgICAgICAgIF90aGlzLnByb3BBY3RpdmVOYW1lID0gJ3NlY29uZCc7CiAgICAgICAgICAgIF90aGlzLmFjdGl2ZU5hbWUgPSAnc2Vjb25kJzsKICAgICAgICAgICAgX3RoaXMuJGZvcmNlVXBkYXRlKCk7CiAgICAgICAgICAgIF90aGlzLmhlYWRCdG5DbGljayg0LCAyKTsKICAgICAgICAgIH0KICAgICAgICB9XQogICAgICB9XSwKICAgICAgdGhyZWF0ZW5BbGFybVN0YXRpc3RpY3NEYXRhOiB7fSwKICAgICAgYXR0YWNrQWxhcm1TdGF0aXN0aWNzRGF0YToge30sCiAgICAgIGhvbmV5cG90QWxhcm1TdGF0aXN0aWNzRGF0YToge30sCiAgICAgIGZpbHRlckxvZ1N0YXRpc3RpY0RhdGE6IHt9LAogICAgICBhcGlBbGFybVN0YXRpc3RpY3NEYXRhOiB7fQogICAgfTsKICB9LAogIHdhdGNoOiB7CiAgICAkcm91dGU6IHsKICAgICAgaGFuZGxlcjogZnVuY3Rpb24gaGFuZGxlcihuZXdWYWwpIHsKICAgICAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgICAgICBpZiAobmV3VmFsLnF1ZXJ5LnR5cGUgPT09ICc0JykgewogICAgICAgICAgLy8g6K6+572u5b2T5YmN6YCJ5Lit5Y2h54mH5ZKM5oyJ6ZKuCiAgICAgICAgICB0aGlzLmN1cnJlbnRDYXJkID0gNDsgLy8g5a+55bqU5a6e5pe26Zi75pat5Y2h54mHCiAgICAgICAgICB0aGlzLmN1cnJlbnRCdG4gPSAxOyAvLyDlr7nlupTmraPlnKjpmLvmlq3mjInpkq4KCiAgICAgICAgICAvLyDlpoLmnpzpnIDopoHop6blj5HmjInpkq7ngrnlh7vpgLvovpEKICAgICAgICAgIHRoaXMuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgLy8g6LCD55So5oyJ6ZKu54K55Ye75pa55rOVCiAgICAgICAgICAgIF90aGlzMi5oZWFkQ2FyZE9wdGlvbnNbM10uYnRuQXJyWzBdLmNsaWNrKCk7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgICAgaWYgKG5ld1ZhbC5xdWVyeS50eXBlID09PSAnMicpIHsKICAgICAgICAgIHRoaXMuY3VycmVudENhcmQgPSAyOwogICAgICAgICAgdGhpcy5jdXJyZW50QnRuID0gMjsKICAgICAgICB9CiAgICAgIH0sCiAgICAgIGltbWVkaWF0ZTogdHJ1ZQogICAgfSwKICAgIGN1cnJlbnRDYXJkOiB7CiAgICAgIGhhbmRsZXI6IGZ1bmN0aW9uIGhhbmRsZXIobmV3VmFsKSB7CiAgICAgICAgLy8g5b2T5YiH5o2i5YiwQVBJ5ZGK6K2m5Y2h54mH5pe277yM5Yi35paw5pWw5o2uCiAgICAgICAgLy8g5rOo6YeK5o6J6L+Z5Liq6LCD55So77yM6YG/5YWN6YeN5aSN6LCD55So57uf6K6h5o6l5Y+j77yM57uf6K6h5pWw5o2u55Sx5a2Q57uE5Lu255qEZ2V0TGlzdOS6i+S7tuinpuWPkQogICAgICAgIC8vIGlmIChuZXdWYWwgPT09IDMpIHsKICAgICAgICAvLyAgIHRoaXMucmVmcmVzaEFwaUFsYXJtU3RhdGlzdGljcygpCiAgICAgICAgLy8gfQogICAgICB9CiAgICB9LAogICAgYWN0aXZlTmFtZTogewogICAgICBoYW5kbGVyOiBmdW5jdGlvbiBoYW5kbGVyKG5ld1ZhbCkgewogICAgICAgIGlmICh0aGlzLmN1cnJlbnRDYXJkID09PSA0KSB7CiAgICAgICAgICBpZiAobmV3VmFsID09PSAnZmlyc3QnKSB7CiAgICAgICAgICAgIHRoaXMuY3VycmVudEJ0biA9IDE7CiAgICAgICAgICB9CiAgICAgICAgICBpZiAobmV3VmFsID09PSAnc2Vjb25kJykgewogICAgICAgICAgICB0aGlzLmN1cnJlbnRCdG4gPSAyOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQogICAgfQogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIHZhciBxdWVyeSA9IHRoaXMuJHJvdXRlLnF1ZXJ5OwogICAgaWYgKHF1ZXJ5KSB7CiAgICAgIHRoaXMuc3JjUXVlcnlQYXJhbXMgPSBxdWVyeTsKICAgICAgdGhpcy5xdWVyeVBhcmFtcyA9ICgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSh7fSwgdGhpcy5zcmNRdWVyeVBhcmFtcyk7CiAgICAgIGlmIChxdWVyeS50YWJzKSB7CiAgICAgICAgdGhpcy5wcm9wQWN0aXZlTmFtZSA9IHF1ZXJ5LnRhYnM7CiAgICAgICAgdGhpcy5hY3RpdmVOYW1lID0gcXVlcnkudGFiczsKICAgICAgfQogICAgfQogICAgdmFyIHBhcmFtcyA9IHsKICAgICAgc3RhcnRUaW1lOiAoMCwgX3J1b3lpLnBhcnNlVGltZSkobmV3IERhdGUoKS5zZXRIb3VycygtMTY4LCAwLCAwLCAwKSwgJ3t5fS17bX0te2R9IDAwOjAwOjAwJyksCiAgICAgIGVuZFRpbWU6ICgwLCBfcnVveWkucGFyc2VUaW1lKShuZXcgRGF0ZSgpLnNldEhvdXJzKDIzLCA1OSwgNTksIDk5OSksICd7eX0te219LXtkfSAyMzo1OTo1OScpLAogICAgICBoYW5kbGVTdGF0ZTogJzAnCiAgICB9OwogICAgdGhpcy5nZXRBbGFybUxldmVsU3RhdGlzdGljcyhwYXJhbXMpOwogICAgdGhpcy5nZXRIb25leXBvdEFsYXJtTGV2ZWxTdGF0aXN0aWNzKHBhcmFtcyk7CiAgICB0aGlzLmdldEZpbHRlckxvZ1N0YXRpc3RpYyh7fSk7CiAgICB0aGlzLmdldEZsb3dSaXNrQXNzZXRzU3RhdGlzdGljcyh7CiAgICAgIHBhcmFtczogewogICAgICAgIGJlZ2luVGltZTogcGFyYW1zLnN0YXJ0VGltZSwKICAgICAgICBlbmRUaW1lOiBwYXJhbXMuZW5kVGltZQogICAgICB9LAogICAgICBoYW5kbGVTdGF0ZTogcGFyYW1zLmhhbmRsZVN0YXRlCiAgICB9KTsKICB9LAogIGNvbXB1dGVkOiB7CiAgICBnZXRHcm91cFN0YXRpc3RpY3NEYXRhOiBmdW5jdGlvbiBnZXRHcm91cFN0YXRpc3RpY3NEYXRhKCkgewogICAgICByZXR1cm4gdGhpcy5hY3RpdmVOYW1lID09PSAnc2Vjb25kJyA/IHRoaXMuYXR0YWNrQWxhcm1TdGF0aXN0aWNzRGF0YSA6IHRoaXMudGhyZWF0ZW5BbGFybVN0YXRpc3RpY3NEYXRhOwogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgZ2V0QWxhcm1MZXZlbFN0YXRpc3RpY3M6IGZ1bmN0aW9uIGdldEFsYXJtTGV2ZWxTdGF0aXN0aWNzKHBhcmFtcykgewogICAgICB2YXIgX3RoaXMzID0gdGhpczsKICAgICAgKDAsIF90aHJlYXQuZ3JvdXBBbGFybUxldmVsU3RhdGlzdGljcykocGFyYW1zKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBfdGhpczMudGhyZWF0ZW5BbGFybVN0YXRpc3RpY3NEYXRhID0gcmVzLmRhdGE7CiAgICAgIH0pOwogICAgfSwKICAgIGdldEF0dGFja0FsYXJtTGV2ZWxTdGF0aXN0aWNzOiBmdW5jdGlvbiBnZXRBdHRhY2tBbGFybUxldmVsU3RhdGlzdGljcyhwYXJhbXMpIHsKICAgICAgdmFyIF90aGlzNCA9IHRoaXM7CiAgICAgICgwLCBfQXR0YWNrQWxhcm0uZ3JvdXBBbGFybUxldmVsU3RhdGlzdGljcykocGFyYW1zKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBfdGhpczQuYXR0YWNrQWxhcm1TdGF0aXN0aWNzRGF0YSA9IHJlcy5kYXRhOwogICAgICB9KTsKICAgIH0sCiAgICBnZXRIb25leXBvdEFsYXJtTGV2ZWxTdGF0aXN0aWNzOiBmdW5jdGlvbiBnZXRIb25leXBvdEFsYXJtTGV2ZWxTdGF0aXN0aWNzKHBhcmFtcykgewogICAgICB2YXIgX3RoaXM1ID0gdGhpczsKICAgICAgKDAsIF90aHJlYXQuZ3JvdXBIb25leXBvdEFsYXJtTGV2ZWxTdGF0aXN0aWNzKShwYXJhbXMpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIF90aGlzNS5ob25leXBvdEFsYXJtU3RhdGlzdGljc0RhdGEgPSByZXMuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgZ2V0RmlsdGVyTG9nU3RhdGlzdGljOiBmdW5jdGlvbiBnZXRGaWx0ZXJMb2dTdGF0aXN0aWMocGFyYW1zKSB7CiAgICAgIHZhciBfdGhpczYgPSB0aGlzOwogICAgICAoMCwgX2Zmc2FmZUlwRmlsdGVyYmxvY2tpbmcuZ2V0RmlsdGVyTG9nU3RhdGlzdGljKShwYXJhbXMpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIF90aGlzNi5maWx0ZXJMb2dTdGF0aXN0aWNEYXRhID0gcmVzLmRhdGE7CiAgICAgIH0pOwogICAgfSwKICAgIGdldEZsb3dSaXNrQXNzZXRzU3RhdGlzdGljczogZnVuY3Rpb24gZ2V0Rmxvd1Jpc2tBc3NldHNTdGF0aXN0aWNzKHBhcmFtcykgewogICAgICB2YXIgX3RoaXM3ID0gdGhpczsKICAgICAgKDAsIF9mbG93Umlza0Fzc2V0cy5nZXRGbG93Umlza0Fzc2V0c1N0YXRpc3RpY3MpKHBhcmFtcykudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgX3RoaXM3LmFwaUFsYXJtU3RhdGlzdGljc0RhdGEgPSByZXMuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlQ2xpY2s6IGZ1bmN0aW9uIGhhbmRsZUNsaWNrKCkgewogICAgICB0aGlzLnByb3BBY3RpdmVOYW1lID0gdGhpcy5hY3RpdmVOYW1lOwogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgcXVlcnk6IHt9CiAgICAgIH0pOwogICAgfSwKICAgIGhlYWRCdG5DbGljazogZnVuY3Rpb24gaGVhZEJ0bkNsaWNrKGNhcmRLZXksIGJ0bktleSkgewogICAgICBpZiAodGhpcy5jdXJyZW50Q2FyZCAhPT0gY2FyZEtleSkgewogICAgICAgIHRoaXMuY3VycmVudEJ0biA9IG51bGw7CiAgICAgIH0KICAgICAgdGhpcy5jdXJyZW50UXVlcnlQYXJhbXMgPSB7fTsKCiAgICAgIC8vIOagueaNruWNoeeJh+exu+Wei+iuvue9ruWvueW6lOeahOm7mOiupOagh+etvumhtQogICAgICBpZiAoY2FyZEtleSA9PT0gMSkgewogICAgICAgIC8vIOa1gemHj+WogeiDgeWRiuitpu+8mumHjee9ruS4uuWRiuitpuWIl+ihqOagh+etvumhtQogICAgICAgIGlmICh0aGlzLmN1cnJlbnRDYXJkICE9PSBjYXJkS2V5KSB7CiAgICAgICAgICB0aGlzLmFjdGl2ZU5hbWUgPSAnZmlyc3QnOwogICAgICAgICAgdGhpcy5wcm9wQWN0aXZlTmFtZSA9ICdmaXJzdCc7CiAgICAgICAgfQogICAgICB9IGVsc2UgaWYgKGNhcmRLZXkgPT09IDIpIHsKICAgICAgICAvLyDonJznvZDor7HmjZXlkYrorabvvJrph43nva7kuLrlkYrorabliJfooajmoIfnrb7pobUKICAgICAgICBpZiAodGhpcy5jdXJyZW50Q2FyZCAhPT0gY2FyZEtleSkgewogICAgICAgICAgdGhpcy5hY3RpdmVOYW1lID0gJ2ZpcnN0JzsKICAgICAgICAgIHRoaXMucHJvcEFjdGl2ZU5hbWUgPSAnZmlyc3QnOwogICAgICAgIH0KICAgICAgfQogICAgICB0aGlzLmN1cnJlbnRDYXJkID0gY2FyZEtleTsKICAgICAgdGhpcy5jdXJyZW50QnRuID0gYnRuS2V5OwogICAgICAvLyBjYXJkS2V5ID09PSAzKEFQSeWRiuitpikg5LiN6ZyA6KaB6K6+572u5qCH562+6aG177yM5Zug5Li65rKh5pyJ5a2Q5qCH562+6aG1CiAgICAgIC8vIGN1cnJlbnRDYXJkID09PSA0ICjlrp7ml7bpmLvmlq0pIOS4jemcgOimgeiuvue9ruagh+etvumhte+8jOWboOS4uuayoeacieWtkOagh+etvumhtQogICAgfSwKICAgIGdldFN0YXRpc3RpY3NWYWx1ZTogZnVuY3Rpb24gZ2V0U3RhdGlzdGljc1ZhbHVlKHNyY0RhdGEsIGtleSkgewogICAgICBpZiAoIXNyY0RhdGEpIHsKICAgICAgICByZXR1cm4gMDsKICAgICAgfQogICAgICByZXR1cm4gc3JjRGF0YVtrZXldIHx8IDA7CiAgICB9LAogICAgaGFuZGxlUmVzZXRCdXR0b246IGZ1bmN0aW9uIGhhbmRsZVJlc2V0QnV0dG9uKCkgewogICAgICAvLyDph43nva5BUEnlkYrorabmjInpkq7pgInkuK3nirbmgIEKICAgICAgaWYgKHRoaXMuY3VycmVudENhcmQgPT09IDMpIHsKICAgICAgICB0aGlzLmN1cnJlbnRCdG4gPSBudWxsOwogICAgICAgIC8vIOmHjee9ruafpeivouWPguaVsOS4reeahOmjjumZqeexu+WeiwogICAgICAgIHRoaXMuY3VycmVudFF1ZXJ5UGFyYW1zLnJpc2tUeXBlID0gbnVsbDsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zID0gKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKCgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSh7fSwgdGhpcy5zcmNRdWVyeVBhcmFtcyksIHRoaXMuY3VycmVudFF1ZXJ5UGFyYW1zKTsKICAgICAgfQogICAgfSwKICAgIGhhbmRsZUFwaVF1ZXJ5Q2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVBcGlRdWVyeUNoYW5nZShxdWVyeUNoYW5nZSkgewogICAgICAvLyDlpITnkIZBUEnlkYrorabmn6Xor6Llj4LmlbDlj5jljJbvvIzlkIzmraXmjInpkq7pgInkuK3nirbmgIEKICAgICAgaWYgKHF1ZXJ5Q2hhbmdlLnJpc2tUeXBlKSB7CiAgICAgICAgLy8g5qC55o2u6aOO6Zmp57G75Z6L6K6+572u5a+55bqU55qE5oyJ6ZKu6YCJ5Lit54q25oCBCiAgICAgICAgc3dpdGNoIChxdWVyeUNoYW5nZS5yaXNrVHlwZSkgewogICAgICAgICAgY2FzZSAnd2Vha19wYXNzd29yZCc6CiAgICAgICAgICAgIHRoaXMuY3VycmVudEJ0biA9IDE7CiAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgY2FzZSAnc2Vuc2l0aXZlX2luZm8nOgogICAgICAgICAgICB0aGlzLmN1cnJlbnRCdG4gPSAyOwogICAgICAgICAgICBicmVhazsKICAgICAgICAgIGNhc2UgJ2hpZ2hfcmlza19hc3NldHMnOgogICAgICAgICAgICB0aGlzLmN1cnJlbnRCdG4gPSAzOwogICAgICAgICAgICBicmVhazsKICAgICAgICAgIGRlZmF1bHQ6CiAgICAgICAgICAgIHRoaXMuY3VycmVudEJ0biA9IG51bGw7CiAgICAgICAgfQogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuY3VycmVudEJ0biA9IG51bGw7CiAgICAgIH0KICAgIH0sCiAgICBoYW5kbGVHZXRFdmVudExpc3Q6IGZ1bmN0aW9uIGhhbmRsZUdldEV2ZW50TGlzdChwYXJhbXMpIHsKICAgICAgaWYgKCFwYXJhbXMuYWxhcm1MZXZlbCkgewogICAgICAgIHRoaXMuY3VycmVudEJ0biA9IG51bGw7CiAgICAgIH0KICAgICAgcGFyYW1zLmFsYXJtTGV2ZWwgPSBudWxsOwogICAgICB0aGlzLmdldEFsYXJtTGV2ZWxTdGF0aXN0aWNzKHBhcmFtcyk7CiAgICB9LAogICAgaGFuZGxlR2V0QXR0YWNrRXZlbnRMaXN0OiBmdW5jdGlvbiBoYW5kbGVHZXRBdHRhY2tFdmVudExpc3QocGFyYW1zKSB7CiAgICAgIGlmICghcGFyYW1zLnJpc2tMZXZlbCkgewogICAgICAgIHRoaXMuY3VycmVudEJ0biA9IG51bGw7CiAgICAgIH0KICAgICAgcGFyYW1zLnJpc2tMZXZlbCA9IG51bGw7CiAgICAgIHRoaXMuZ2V0QXR0YWNrQWxhcm1MZXZlbFN0YXRpc3RpY3MocGFyYW1zKTsKICAgIH0sCiAgICBoYW5kbGVHZXRIb25leUV2ZW50TGlzdDogZnVuY3Rpb24gaGFuZGxlR2V0SG9uZXlFdmVudExpc3QocGFyYW1zKSB7CiAgICAgIGlmICghcGFyYW1zLmFsYXJtTGV2ZWwpIHsKICAgICAgICB0aGlzLmN1cnJlbnRCdG4gPSBudWxsOwogICAgICB9CiAgICAgIHBhcmFtcy5hbGFybUxldmVsID0gbnVsbDsKICAgICAgdGhpcy5nZXRIb25leXBvdEFsYXJtTGV2ZWxTdGF0aXN0aWNzKHBhcmFtcyk7CiAgICB9LAogICAgaGFuZGxlR2V0QXBpRXZlbnRMaXN0OiBmdW5jdGlvbiBoYW5kbGVHZXRBcGlFdmVudExpc3QocGFyYW1zKSB7CiAgICAgIC8vIOaehOW7uue7n+iuoeaOpeWPo+eahOafpeivouWPguaVsO+8jOWMheWQq+aJgOacieafpeivouadoeS7tgogICAgICB2YXIgc3RhdGlzdGljc1BhcmFtcyA9IHsKICAgICAgICBwYXJhbXM6IHsKICAgICAgICAgIGJlZ2luVGltZTogcGFyYW1zLnBhcmFtcyA/IHBhcmFtcy5wYXJhbXMuYmVnaW5UaW1lIDogdW5kZWZpbmVkLAogICAgICAgICAgZW5kVGltZTogcGFyYW1zLnBhcmFtcyA/IHBhcmFtcy5wYXJhbXMuZW5kVGltZSA6IHVuZGVmaW5lZAogICAgICAgIH0sCiAgICAgICAgZGV2aWNlQ29uZmlnSWQ6IHBhcmFtcy5kZXZpY2VDb25maWdJZAogICAgICB9OwogICAgICAvLyDkvKDpgJLmiYDmnInmn6Xor6LmnaHku7bnu5nnu5/orqHmjqXlj6MKICAgICAgaWYgKHBhcmFtcy5yaXNrQXNzZXRzKSB7CiAgICAgICAgc3RhdGlzdGljc1BhcmFtcy5yaXNrQXNzZXRzID0gcGFyYW1zLnJpc2tBc3NldHM7CiAgICAgIH0KICAgICAgaWYgKHBhcmFtcy5oYW5kbGVTdGF0ZSAhPT0gdW5kZWZpbmVkICYmIHBhcmFtcy5oYW5kbGVTdGF0ZSAhPT0gbnVsbCkgewogICAgICAgIHN0YXRpc3RpY3NQYXJhbXMuaGFuZGxlU3RhdGUgPSBwYXJhbXMuaGFuZGxlU3RhdGU7CiAgICAgIH0KICAgICAgcGFyYW1zLnJpc2tUeXBlID0gbnVsbDsKICAgICAgdGhpcy5nZXRGbG93Umlza0Fzc2V0c1N0YXRpc3RpY3Moc3RhdGlzdGljc1BhcmFtcyk7CiAgICB9LAogICAgaGFuZGxlR2V0SXBGaWx0ZXJMaXN0OiBmdW5jdGlvbiBoYW5kbGVHZXRJcEZpbHRlckxpc3QocGFyYW1zKSB7CiAgICAgIHRoaXMuZ2V0RmlsdGVyTG9nU3RhdGlzdGljKHBhcmFtcyk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["_eventList", "_interopRequireDefault", "require", "_attackViewList", "_sufferViewList", "_index", "_index2", "_history", "_threat", "_AttackAlarm", "_honeypotAlarmList", "_honeypotAttackViewList", "_apiAlarmList", "_ffsafeIpFilterblocking", "_flowRiskAssets", "_ruoyi", "name", "components", "SufferViewList", "AttackViewList", "EventList", "Asset<PERSON>iew", "IpfilterLog", "IpFilterLogHistory", "HoneypotAlarmList", "HoneypotAttackViewList", "ApiAlarmList", "data", "_this", "activeName", "propActiveName", "srcQueryParams", "queryParams", "currentQueryParams", "currentCard", "currentBtn", "headCardOptions", "title", "total", "getStatisticsValue", "getGroupStatisticsData", "key", "click", "headBtnClick", "alarmLevel", "riskLevel", "_objectSpread2", "default", "btnArr", "icon", "value", "label", "honeypotAlarmStatisticsData", "apiAlarmStatisticsData", "riskType", "filterLogStatisticData", "$forceUpdate", "threatenAlarmStatisticsData", "attackAlarmStatisticsData", "watch", "$route", "handler", "newVal", "_this2", "query", "type", "$nextTick", "immediate", "mounted", "tabs", "params", "startTime", "parseTime", "Date", "setHours", "endTime", "handleState", "getAlarmLevelStatistics", "getHoneypotAlarmLevelStatistics", "getFilterLogStatistic", "getFlowRiskAssetsStatistics", "beginTime", "computed", "methods", "_this3", "groupAlarmLevelStatistics", "then", "res", "getAttackAlarmLevelStatistics", "_this4", "groupAttackAlarmLevelStatistics", "_this5", "groupHoneypotAlarmLevelStatistics", "_this6", "_this7", "handleClick", "$router", "push", "card<PERSON><PERSON>", "btnKey", "srcData", "handleResetButton", "handleApiQueryChange", "query<PERSON>hange", "handleGetEventList", "handleGetAttackEventList", "handleGetHoneyEventList", "handleGetApiEventList", "statisticsParams", "undefined", "deviceConfigId", "riskAssets", "handleGetIpFilterList"], "sources": ["src/views/frailty/event/alertEvent.vue"], "sourcesContent": ["<template>\n  <div class=\"alert-event-box\">\n    <el-row :gutter=\"8\" style=\"margin-bottom: 8px;\">\n      <el-col v-for=\"(headItem,index) in headCardOptions\" :key=\"'head-'+index\" :span=\"6\">\n        <div :class=\"currentCard === headItem.key ? 'head-card active' : 'head-card'\" @click=\"headItem.click\">\n          <div class=\"head-card-title\">\n            {{ headItem.title }}\n            <span style=\"margin-left: 5px\">\n              <el-tag type=\"primary\" effect=\"dark\" size=\"mini\">{{ headItem.total() }}</el-tag>\n            </span>\n          </div>\n          <div class=\"head-card-btn-box\">\n            <el-row :gutter=\"20\" style=\"height: 100%;display: flex;align-items: center;margin-left: 0;margin-right: 0\">\n              <el-col\n                v-for=\"(btnItem,btnIndex) in headItem.btnArr\"\n                :key=\"'btn-'+btnIndex\"\n                :span=\"24/headItem.btnArr.length\"\n                :class=\"currentCard === headItem.key && currentBtn === btnItem.key ? 'head-btn-active' : ''\"\n                style=\"padding-top: 10px;padding-bottom: 10px;\"\n              >\n                <div class=\"head-card-btn\" @click.stop=\"btnItem.click\">\n                  <div :class=\"[ btnItem.label === '弱口令账号' ? 'btn-icon1' : 'btn-icon']\">\n                    <el-image :src=\"btnItem.icon\" />\n                  </div>\n                  <div class=\"btn-content\">\n                    <div class=\"btn-content-value\">\n                      {{ btnItem.value() }}\n                    </div>\n                    <div class=\"btn-content-label\">\n                      {{ btnItem.label }}\n                    </div>\n                  </div>\n                </div>\n              </el-col>\n            </el-row>\n          </div>\n        </div>\n      </el-col>\n    </el-row>\n    <div v-if=\"currentCard === 1\" class=\"box-content\">\n      <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\">\n        <el-tab-pane label=\"告警列表\" name=\"first\" />\n        <el-tab-pane label=\"攻击者视角\" name=\"second\" />\n        <el-tab-pane label=\"受害者视角\" name=\"third\" />\n        <el-tab-pane label=\"资产视角\" name=\"four\" />\n        <!--        <el-tab-pane label=\"阻断IP\" name=\"five\"></el-tab-pane>-->\n      </el-tabs>\n      <div style=\"height: calc(100% - 41px); margin-top: 3px\">\n        <event-list v-if=\"propActiveName === 'first'\" :props-active-name=\"propActiveName\" :props-query-params=\"queryParams\" :current-btn.sync=\"currentBtn\" @getList=\"handleGetEventList\" />\n        <attack-view-list v-if=\"propActiveName === 'second'\" :props-active-name=\"propActiveName\" :props-query-params=\"queryParams\" :current-btn.sync=\"currentBtn\" @getList=\"handleGetAttackEventList\" />\n        <suffer-view-list v-if=\"propActiveName === 'third'\" :props-active-name=\"propActiveName\" :props-query-params=\"queryParams\" :current-btn.sync=\"currentBtn\" @getList=\"handleGetEventList\" />\n        <asset-view v-if=\"propActiveName === 'four'\" :props-active-name=\"propActiveName\" :props-query-params=\"queryParams\" :current-btn.sync=\"currentBtn\" @getList=\"handleGetEventList\" />\n      </div>\n    </div>\n    <div v-if=\"currentCard === 2\" class=\"box-content\">\n      <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\">\n        <el-tab-pane label=\"告警列表\" name=\"first\" />\n        <el-tab-pane label=\"攻击者视角\" name=\"second\" />\n      </el-tabs>\n      <div style=\"height: calc(100% - 41px); margin-top: 3px\">\n        <honeypotAlarmList v-if=\"propActiveName === 'first'\" :props-active-name=\"propActiveName\" :props-query-params=\"queryParams\" :current-btn.sync=\"currentBtn\" @getList=\"handleGetHoneyEventList\" />\n        <honeypot-attack-view-list v-if=\"propActiveName === 'second'\" :props-active-name=\"propActiveName\" :props-query-params=\"queryParams\" :current-btn.sync=\"currentBtn\" @getList=\"handleGetHoneyEventList\" />\n      </div>\n    </div>\n    <div v-if=\"currentCard === 3\" class=\"box-content\">\n      <api-alarm-list v-if=\"currentCard === 3\" :props-active-name=\"'apiAlarm'\" :props-query-params=\"queryParams\" @reset-button=\"handleResetButton\" @query-change=\"handleApiQueryChange\" @getList=\"handleGetApiEventList\" />\n    </div>\n    <div v-if=\"currentCard === 4\" class=\"box-content\">\n      <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\">\n        <el-tab-pane label=\"阻断中列表\" name=\"first\" />\n        <el-tab-pane label=\"阻断历史记录\" name=\"second\" />\n      </el-tabs>\n      <div style=\"height: calc(100% - 47px); margin-top: 8px\">\n        <IpfilterLog v-if=\"propActiveName === 'first'\" ref=\"ipFilterLog\" :props-active-name=\"propActiveName\" @getList=\"handleGetIpFilterList\" />\n        <IpFilterLogHistory v-if=\"propActiveName === 'second'\" ref=\"ipFilterLogHistory\" :props-active-name=\"propActiveName\" @getList=\"handleGetIpFilterList\" />\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport EventList from './component/eventList'\nimport AttackViewList from './component/attackViewList'\nimport SufferViewList from './component/sufferViewList'\nimport AssetView from '@/views/threat/asset/index'\nimport IpfilterLog from '@/views/aqsoc/ffsafe-ipfilter-log/index'\nimport IpFilterLogHistory from '@/views/aqsoc/ffsafe-ipfilter-log/history'\nimport { groupAlarmLevelStatistics, groupHoneypotAlarmLevelStatistics } from '@/api/threat/threat'\nimport { groupAlarmLevelStatistics as groupAttackAlarmLevelStatistics } from '@/api/threaten/AttackAlarm';\nimport HoneypotAlarmList from './component/honeypotAlarmList'\nimport HoneypotAttackViewList from './component/honeypotAttackViewList'\nimport ApiAlarmList from './component/apiAlarmList'\nimport { getFilterLogStatistic } from '@/api/safe/ffsafeIpFilterblocking'\nimport { getFlowRiskAssetsStatistics } from '@/api/ffsafe/flowRiskAssets'\nimport { parseTime } from '@/utils/ruoyi'\n\nexport default {\n  name: 'AlertEvent',\n  components: { SufferViewList, AttackViewList, EventList, AssetView, IpfilterLog, IpFilterLogHistory, HoneypotAlarmList, HoneypotAttackViewList, ApiAlarmList },\n  data() {\n    return {\n      activeName: 'first',\n      propActiveName: 'first',\n      srcQueryParams: {},\n      queryParams: {},\n      currentQueryParams: {},\n      currentCard: 1,\n      currentBtn: null,\n      headCardOptions: [\n        {\n          title: '流量威胁告警',\n          total: () => this.getStatisticsValue(this.getGroupStatisticsData, 'total'),\n          key: 1,\n          click: () => {\n            this.headBtnClick(1, null)\n            this.currentQueryParams.alarmLevel = null;\n            this.currentQueryParams.riskLevel = null;\n            this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n          },\n          btnArr: [\n            /*{\n              icon: require('@/assets/icons/event/level5.png'),\n              value: () => this.getStatisticsValue(this.getGroupStatisticsData, 'alarmLevel5'),\n              label: '严重',\n              key: 5,\n              click: () => {\n                this.headBtnClick(1, 5)\n                this.currentQueryParams.alarmLevel = '5'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            },*/\n            {\n              icon: require('@/assets/icons/event/level4.png'),\n              value: () => this.getStatisticsValue(this.getGroupStatisticsData, 'alarmLevel4'),\n              label: '高危',\n              key: 4,\n              click: () => {\n                this.headBtnClick(1, 4)\n                this.currentQueryParams.alarmLevel = '4'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            },\n            {\n              icon: require('@/assets/icons/event/level3.png'),\n              value: () => this.getStatisticsValue(this.getGroupStatisticsData, 'alarmLevel3'),\n              label: '中危',\n              key: 3,\n              click: () => {\n                this.headBtnClick(1, 3)\n                this.currentQueryParams.alarmLevel = '3'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            },\n            {\n              icon: require('@/assets/icons/event/level2.png'),\n              value: () => this.getStatisticsValue(this.getGroupStatisticsData, 'alarmLevel2'),\n              label: '低危',\n              key: 2,\n              click: () => {\n                this.headBtnClick(1, 2)\n                this.currentQueryParams.alarmLevel = '2'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            }\n          ]\n        },\n        {\n          title: '蜜罐诱捕告警',\n          total: () => this.getStatisticsValue(this.honeypotAlarmStatisticsData, 'total'),\n          key: 2,\n          click: () => this.headBtnClick(2, null),\n          btnArr: [\n            {\n              icon: require('@/assets/icons/event/level5.png'),\n              value: () => this.getStatisticsValue(this.honeypotAlarmStatisticsData, 'alarmLevel5'),\n              label: '严重',\n              key: 5,\n              click: () => {\n                this.headBtnClick(2, 5)\n                this.currentQueryParams.alarmLevel = '5'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            },\n            {\n              icon: require('@/assets/icons/event/level4.png'),\n              value: () => this.getStatisticsValue(this.honeypotAlarmStatisticsData, 'alarmLevel4'),\n              label: '高危',\n              key: 4,\n              click: () => {\n                this.headBtnClick(2, 4)\n                this.currentQueryParams.alarmLevel = '4'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            },\n            {\n              icon: require('@/assets/icons/event/level3.png'),\n              value: () => this.getStatisticsValue(this.honeypotAlarmStatisticsData, 'alarmLevel3'),\n              label: '中危',\n              key: 3,\n              click: () => {\n                this.headBtnClick(2, 3)\n                this.currentQueryParams.alarmLevel = '3'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            },\n            {\n              icon: require('@/assets/icons/event/level2.png'),\n              value: () => this.getStatisticsValue(this.honeypotAlarmStatisticsData, 'alarmLevel2'),\n              label: '低危',\n              key: 2,\n              click: () => {\n                this.headBtnClick(2, 2)\n                this.currentQueryParams.alarmLevel = '2'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            }\n          ]\n        },\n        {\n          title: 'API告警',\n          total: () => this.getStatisticsValue(this.apiAlarmStatisticsData, 'total'),\n          key: 3,\n          click: () => {\n            this.headBtnClick(3, null)\n            this.currentQueryParams.riskType = null\n            this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n          },\n          btnArr: [\n            {\n              icon: require('@/assets/icons/event/weakPassword.png'),\n              value: () => this.getStatisticsValue(this.apiAlarmStatisticsData, 'weakPassword'),\n              label: '弱口令账号',\n              key: 1,\n              click: () => {\n                this.headBtnClick(3, 1)\n                this.currentQueryParams.riskType = 'weak_password'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            },\n            {\n              icon: require('@/assets/icons/event/敏感信息.png'),\n              value: () => this.getStatisticsValue(this.apiAlarmStatisticsData, 'sensitiveInfo'),\n              label: '敏感信息',\n              key: 2,\n              click: () => {\n                this.headBtnClick(3, 2)\n                this.currentQueryParams.riskType = 'sensitive_info'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            },\n            {\n              icon: require('@/assets/icons/event/高危资产.png'),\n              value: () => this.getStatisticsValue(this.apiAlarmStatisticsData, 'highRiskAssets'),\n              label: '高危资产',\n              key: 3,\n              click: () => {\n                this.headBtnClick(3, 3)\n                this.currentQueryParams.riskType = 'high_risk_assets'\n                this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n              }\n            }\n          ]\n        },\n        {\n          title: '实时阻断',\n          total: () => this.getStatisticsValue(this.filterLogStatisticData, 'total'),\n          key: 4,\n          click: () => {\n            this.headBtnClick(4, null)\n            this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n          },\n          btnArr: [\n            {\n              icon: require('@/assets/icons/event/正在阻断.png'),\n              value: () => this.getStatisticsValue(this.filterLogStatisticData, 'blockingCount'),\n              label: '正在阻断',\n              key: 1,\n              click: () => {\n                this.propActiveName = 'first'\n                this.activeName = 'first'\n                this.headBtnClick(4, 1)\n              }\n            },\n            {\n              icon: require('@/assets/icons/event/阻断历史.png'),\n              value: () => this.getStatisticsValue(this.filterLogStatisticData, 'blockLogCount'),\n              label: '历史记录',\n              key: 2,\n              click: () => {\n                this.propActiveName = 'second'\n                this.activeName = 'second'\n                this.$forceUpdate()\n                this.headBtnClick(4, 2)\n              }\n            }\n          ]\n        }\n      ],\n      threatenAlarmStatisticsData: {},\n      attackAlarmStatisticsData: {},\n      honeypotAlarmStatisticsData: {},\n      filterLogStatisticData: {},\n      apiAlarmStatisticsData: {}\n    }\n  },\n  watch: {\n    $route: {\n      handler(newVal) {\n        if (newVal.query.type === '4') {\n          // 设置当前选中卡片和按钮\n          this.currentCard = 4 // 对应实时阻断卡片\n          this.currentBtn = 1 // 对应正在阻断按钮\n\n          // 如果需要触发按钮点击逻辑\n          this.$nextTick(() => {\n            // 调用按钮点击方法\n            this.headCardOptions[3].btnArr[0].click()\n          })\n        }\n        if (newVal.query.type === '2') {\n          this.currentCard = 2\n          this.currentBtn = 2\n        }\n      },\n      immediate: true\n    },\n    currentCard: {\n      handler(newVal) {\n        // 当切换到API告警卡片时，刷新数据\n        // 注释掉这个调用，避免重复调用统计接口，统计数据由子组件的getList事件触发\n        // if (newVal === 3) {\n        //   this.refreshApiAlarmStatistics()\n        // }\n      }\n    },\n    activeName: {\n      handler(newVal) {\n        if (this.currentCard === 4) {\n          if (newVal === 'first') {\n            this.currentBtn = 1\n          }\n          if (newVal === 'second') {\n            this.currentBtn = 2\n          }\n        }\n      }\n    }\n  },\n  mounted() {\n    const query = this.$route.query\n    if (query) {\n      this.srcQueryParams = query\n      this.queryParams = { ...this.srcQueryParams }\n      if (query.tabs) {\n        this.propActiveName = query.tabs\n        this.activeName = query.tabs\n      }\n    }\n    const params = {\n      startTime: parseTime(new Date().setHours(-168, 0, 0, 0), '{y}-{m}-{d} 00:00:00'),\n      endTime: parseTime(new Date().setHours(23, 59, 59, 999), '{y}-{m}-{d} 23:59:59'),\n      handleState: '0'\n    }\n    this.getAlarmLevelStatistics(params)\n    this.getHoneypotAlarmLevelStatistics(params)\n    this.getFilterLogStatistic({})\n    this.getFlowRiskAssetsStatistics({\n      params: {\n        beginTime: params.startTime,\n        endTime: params.endTime\n      },\n      handleState: params.handleState\n    })\n  },\n  computed: {\n    getGroupStatisticsData() {\n      return this.activeName === 'second' ? this.attackAlarmStatisticsData : this.threatenAlarmStatisticsData;\n    }\n  },\n  methods: {\n    getAlarmLevelStatistics(params) {\n      groupAlarmLevelStatistics(params).then(res => {\n        this.threatenAlarmStatisticsData = res.data\n      })\n    },\n    getAttackAlarmLevelStatistics(params) {\n      groupAttackAlarmLevelStatistics(params).then(res => {\n        this.attackAlarmStatisticsData = res.data\n      })\n    },\n    getHoneypotAlarmLevelStatistics(params) {\n      groupHoneypotAlarmLevelStatistics(params).then(res => {\n        this.honeypotAlarmStatisticsData = res.data\n      })\n    },\n    getFilterLogStatistic(params) {\n      getFilterLogStatistic(params).then(res => {\n        this.filterLogStatisticData = res.data\n      })\n    },\n    getFlowRiskAssetsStatistics(params) {\n      getFlowRiskAssetsStatistics(params).then(res => {\n        this.apiAlarmStatisticsData = res.data\n      })\n    },\n    handleClick() {\n      this.propActiveName = this.activeName\n      this.$router.push({ query: {}})\n    },\n    headBtnClick(cardKey, btnKey) {\n      if (this.currentCard !== cardKey) {\n        this.currentBtn = null\n      }\n      this.currentQueryParams = {}\n\n      // 根据卡片类型设置对应的默认标签页\n      if (cardKey === 1) {\n        // 流量威胁告警：重置为告警列表标签页\n        if (this.currentCard !== cardKey) {\n          this.activeName = 'first'\n          this.propActiveName = 'first'\n        }\n      } else if (cardKey === 2) {\n        // 蜜罐诱捕告警：重置为告警列表标签页\n        if (this.currentCard !== cardKey) {\n          this.activeName = 'first'\n          this.propActiveName = 'first'\n        }\n      }\n      this.currentCard = cardKey\n      this.currentBtn = btnKey\n      // cardKey === 3(API告警) 不需要设置标签页，因为没有子标签页\n      // currentCard === 4 (实时阻断) 不需要设置标签页，因为没有子标签页\n    },\n    getStatisticsValue(srcData, key) {\n      if (!srcData) {\n        return 0\n      }\n      return srcData[key] || 0\n    },\n    handleResetButton() {\n      // 重置API告警按钮选中状态\n      if (this.currentCard === 3) {\n        this.currentBtn = null\n        // 重置查询参数中的风险类型\n        this.currentQueryParams.riskType = null\n        this.queryParams = { ...this.srcQueryParams, ...this.currentQueryParams }\n      }\n    },\n    handleApiQueryChange(queryChange) {\n      // 处理API告警查询参数变化，同步按钮选中状态\n      if (queryChange.riskType) {\n        // 根据风险类型设置对应的按钮选中状态\n        switch (queryChange.riskType) {\n          case 'weak_password':\n            this.currentBtn = 1\n            break\n          case 'sensitive_info':\n            this.currentBtn = 2\n            break\n          case 'high_risk_assets':\n            this.currentBtn = 3\n            break\n          default:\n            this.currentBtn = null\n        }\n      } else {\n        this.currentBtn = null\n      }\n    },\n    handleGetEventList(params) {\n      if (!params.alarmLevel) {\n        this.currentBtn = null\n      }\n      params.alarmLevel = null\n      this.getAlarmLevelStatistics(params)\n    },\n    handleGetAttackEventList(params) {\n      if (!params.riskLevel) {\n        this.currentBtn = null\n      }\n      params.riskLevel = null\n      this.getAttackAlarmLevelStatistics(params)\n    },\n    handleGetHoneyEventList(params) {\n      if (!params.alarmLevel) {\n        this.currentBtn = null\n      }\n      params.alarmLevel = null\n      this.getHoneypotAlarmLevelStatistics(params)\n    },\n    handleGetApiEventList(params) {\n      // 构建统计接口的查询参数，包含所有查询条件\n      const statisticsParams = {\n        params: {\n          beginTime: params.params ? params.params.beginTime : undefined,\n          endTime: params.params ? params.params.endTime : undefined\n        },\n        deviceConfigId: params.deviceConfigId\n      }\n      // 传递所有查询条件给统计接口\n      if (params.riskAssets) {\n        statisticsParams.riskAssets = params.riskAssets\n      }\n\n      if (params.handleState !== undefined && params.handleState !== null) {\n        statisticsParams.handleState = params.handleState\n      }\n\n      params.riskType = null\n      this.getFlowRiskAssetsStatistics(statisticsParams)\n    },\n    handleGetIpFilterList(params) {\n      this.getFilterLogStatistic(params)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"../../../assets/styles/tabs.scss\";\n@font-face {\n  font-family: electronicFont;\n  src: url(../../../assets/fonts/DS-DIGI.ttf);\n}\n\n.alert-event-box {\n  width: 100%;\n  height: 100%;\n  overflow: hidden;\n  .box-content {\n    height: calc(100% - 115px);\n  }\n}\n\n.head-card{\n  //height: 119px;\n  background-color: #FFFFFF;\n  padding: 8px 10px;\n  font-size: 14px;\n  display: flex;\n  flex-direction: column;\n  cursor: pointer;\n  .head-card-title{\n    color: #242424;\n    font-size: 14px;\n    font-weight: 700;\n  }\n  .head-card-btn-box{\n    flex: 1;\n    margin-top: 10px;\n    margin-bottom: 5px;\n    .head-card-btn{\n      display: flex;\n      cursor: pointer;\n      .btn-icon{\n        width: 50%;\n        text-align: center;\n        .el-image{\n          width: 32px;\n          height: 32px;\n        }\n      }\n      .btn-icon1{\n        width: 35px;\n        text-align: center;\n        .el-image{\n          width: 32px;\n          height: 32px;\n        }\n      }\n      .btn-content{\n        padding-left: 5px;\n        display: flex;\n        flex-direction: column;\n        position: relative;\n        flex: 1;\n        .btn-content-value{\n          font-size: 18px;\n          font-weight: 700;\n          font-family: electronicFont;\n        }\n        .btn-content-label{\n          position: absolute;\n          bottom: 0;\n          font-weight: 400;\n          white-space: nowrap;\n        }\n      }\n    }\n\n    .head-btn-active{\n      background-color: #f3f8fe;\n    }\n  }\n}\n.active{\n  border: 1px solid #4382FD;\n}\n</style>\n"], "mappings": ";;;;;;;;AAiFA,IAAAA,UAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,eAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,eAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,MAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,OAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,QAAA,GAAAN,sBAAA,CAAAC,OAAA;AACA,IAAAM,OAAA,GAAAN,OAAA;AACA,IAAAO,YAAA,GAAAP,OAAA;AACA,IAAAQ,kBAAA,GAAAT,sBAAA,CAAAC,OAAA;AACA,IAAAS,uBAAA,GAAAV,sBAAA,CAAAC,OAAA;AACA,IAAAU,aAAA,GAAAX,sBAAA,CAAAC,OAAA;AACA,IAAAW,uBAAA,GAAAX,OAAA;AACA,IAAAY,eAAA,GAAAZ,OAAA;AACA,IAAAa,MAAA,GAAAb,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAc,IAAA;EACAC,UAAA;IAAAC,cAAA,EAAAA,uBAAA;IAAAC,cAAA,EAAAA,uBAAA;IAAAC,SAAA,EAAAA,kBAAA;IAAAC,SAAA,EAAAA,cAAA;IAAAC,WAAA,EAAAA,eAAA;IAAAC,kBAAA,EAAAA,gBAAA;IAAAC,iBAAA,EAAAA,0BAAA;IAAAC,sBAAA,EAAAA,+BAAA;IAAAC,YAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,UAAA;MACAC,cAAA;MACAC,cAAA;MACAC,WAAA;MACAC,kBAAA;MACAC,WAAA;MACAC,UAAA;MACAC,eAAA,GACA;QACAC,KAAA;QACAC,KAAA,WAAAA,MAAA;UAAA,OAAAV,KAAA,CAAAW,kBAAA,CAAAX,KAAA,CAAAY,sBAAA;QAAA;QACAC,GAAA;QACAC,KAAA,WAAAA,MAAA;UACAd,KAAA,CAAAe,YAAA;UACAf,KAAA,CAAAK,kBAAA,CAAAW,UAAA;UACAhB,KAAA,CAAAK,kBAAA,CAAAY,SAAA;UACAjB,KAAA,CAAAI,WAAA,OAAAc,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAnB,KAAA,CAAAG,cAAA,GAAAH,KAAA,CAAAK,kBAAA;QACA;QACAe,MAAA;QACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACA;UACAC,IAAA,EAAA/C,OAAA;UACAgD,KAAA,WAAAA,MAAA;YAAA,OAAAtB,KAAA,CAAAW,kBAAA,CAAAX,KAAA,CAAAY,sBAAA;UAAA;UACAW,KAAA;UACAV,GAAA;UACAC,KAAA,WAAAA,MAAA;YACAd,KAAA,CAAAe,YAAA;YACAf,KAAA,CAAAK,kBAAA,CAAAW,UAAA;YACAhB,KAAA,CAAAI,WAAA,OAAAc,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAnB,KAAA,CAAAG,cAAA,GAAAH,KAAA,CAAAK,kBAAA;UACA;QACA,GACA;UACAgB,IAAA,EAAA/C,OAAA;UACAgD,KAAA,WAAAA,MAAA;YAAA,OAAAtB,KAAA,CAAAW,kBAAA,CAAAX,KAAA,CAAAY,sBAAA;UAAA;UACAW,KAAA;UACAV,GAAA;UACAC,KAAA,WAAAA,MAAA;YACAd,KAAA,CAAAe,YAAA;YACAf,KAAA,CAAAK,kBAAA,CAAAW,UAAA;YACAhB,KAAA,CAAAI,WAAA,OAAAc,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAnB,KAAA,CAAAG,cAAA,GAAAH,KAAA,CAAAK,kBAAA;UACA;QACA,GACA;UACAgB,IAAA,EAAA/C,OAAA;UACAgD,KAAA,WAAAA,MAAA;YAAA,OAAAtB,KAAA,CAAAW,kBAAA,CAAAX,KAAA,CAAAY,sBAAA;UAAA;UACAW,KAAA;UACAV,GAAA;UACAC,KAAA,WAAAA,MAAA;YACAd,KAAA,CAAAe,YAAA;YACAf,KAAA,CAAAK,kBAAA,CAAAW,UAAA;YACAhB,KAAA,CAAAI,WAAA,OAAAc,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAnB,KAAA,CAAAG,cAAA,GAAAH,KAAA,CAAAK,kBAAA;UACA;QACA;MAEA,GACA;QACAI,KAAA;QACAC,KAAA,WAAAA,MAAA;UAAA,OAAAV,KAAA,CAAAW,kBAAA,CAAAX,KAAA,CAAAwB,2BAAA;QAAA;QACAX,GAAA;QACAC,KAAA,WAAAA,MAAA;UAAA,OAAAd,KAAA,CAAAe,YAAA;QAAA;QACAK,MAAA,GACA;UACAC,IAAA,EAAA/C,OAAA;UACAgD,KAAA,WAAAA,MAAA;YAAA,OAAAtB,KAAA,CAAAW,kBAAA,CAAAX,KAAA,CAAAwB,2BAAA;UAAA;UACAD,KAAA;UACAV,GAAA;UACAC,KAAA,WAAAA,MAAA;YACAd,KAAA,CAAAe,YAAA;YACAf,KAAA,CAAAK,kBAAA,CAAAW,UAAA;YACAhB,KAAA,CAAAI,WAAA,OAAAc,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAnB,KAAA,CAAAG,cAAA,GAAAH,KAAA,CAAAK,kBAAA;UACA;QACA,GACA;UACAgB,IAAA,EAAA/C,OAAA;UACAgD,KAAA,WAAAA,MAAA;YAAA,OAAAtB,KAAA,CAAAW,kBAAA,CAAAX,KAAA,CAAAwB,2BAAA;UAAA;UACAD,KAAA;UACAV,GAAA;UACAC,KAAA,WAAAA,MAAA;YACAd,KAAA,CAAAe,YAAA;YACAf,KAAA,CAAAK,kBAAA,CAAAW,UAAA;YACAhB,KAAA,CAAAI,WAAA,OAAAc,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAnB,KAAA,CAAAG,cAAA,GAAAH,KAAA,CAAAK,kBAAA;UACA;QACA,GACA;UACAgB,IAAA,EAAA/C,OAAA;UACAgD,KAAA,WAAAA,MAAA;YAAA,OAAAtB,KAAA,CAAAW,kBAAA,CAAAX,KAAA,CAAAwB,2BAAA;UAAA;UACAD,KAAA;UACAV,GAAA;UACAC,KAAA,WAAAA,MAAA;YACAd,KAAA,CAAAe,YAAA;YACAf,KAAA,CAAAK,kBAAA,CAAAW,UAAA;YACAhB,KAAA,CAAAI,WAAA,OAAAc,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAnB,KAAA,CAAAG,cAAA,GAAAH,KAAA,CAAAK,kBAAA;UACA;QACA,GACA;UACAgB,IAAA,EAAA/C,OAAA;UACAgD,KAAA,WAAAA,MAAA;YAAA,OAAAtB,KAAA,CAAAW,kBAAA,CAAAX,KAAA,CAAAwB,2BAAA;UAAA;UACAD,KAAA;UACAV,GAAA;UACAC,KAAA,WAAAA,MAAA;YACAd,KAAA,CAAAe,YAAA;YACAf,KAAA,CAAAK,kBAAA,CAAAW,UAAA;YACAhB,KAAA,CAAAI,WAAA,OAAAc,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAnB,KAAA,CAAAG,cAAA,GAAAH,KAAA,CAAAK,kBAAA;UACA;QACA;MAEA,GACA;QACAI,KAAA;QACAC,KAAA,WAAAA,MAAA;UAAA,OAAAV,KAAA,CAAAW,kBAAA,CAAAX,KAAA,CAAAyB,sBAAA;QAAA;QACAZ,GAAA;QACAC,KAAA,WAAAA,MAAA;UACAd,KAAA,CAAAe,YAAA;UACAf,KAAA,CAAAK,kBAAA,CAAAqB,QAAA;UACA1B,KAAA,CAAAI,WAAA,OAAAc,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAnB,KAAA,CAAAG,cAAA,GAAAH,KAAA,CAAAK,kBAAA;QACA;QACAe,MAAA,GACA;UACAC,IAAA,EAAA/C,OAAA;UACAgD,KAAA,WAAAA,MAAA;YAAA,OAAAtB,KAAA,CAAAW,kBAAA,CAAAX,KAAA,CAAAyB,sBAAA;UAAA;UACAF,KAAA;UACAV,GAAA;UACAC,KAAA,WAAAA,MAAA;YACAd,KAAA,CAAAe,YAAA;YACAf,KAAA,CAAAK,kBAAA,CAAAqB,QAAA;YACA1B,KAAA,CAAAI,WAAA,OAAAc,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAnB,KAAA,CAAAG,cAAA,GAAAH,KAAA,CAAAK,kBAAA;UACA;QACA,GACA;UACAgB,IAAA,EAAA/C,OAAA;UACAgD,KAAA,WAAAA,MAAA;YAAA,OAAAtB,KAAA,CAAAW,kBAAA,CAAAX,KAAA,CAAAyB,sBAAA;UAAA;UACAF,KAAA;UACAV,GAAA;UACAC,KAAA,WAAAA,MAAA;YACAd,KAAA,CAAAe,YAAA;YACAf,KAAA,CAAAK,kBAAA,CAAAqB,QAAA;YACA1B,KAAA,CAAAI,WAAA,OAAAc,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAnB,KAAA,CAAAG,cAAA,GAAAH,KAAA,CAAAK,kBAAA;UACA;QACA,GACA;UACAgB,IAAA,EAAA/C,OAAA;UACAgD,KAAA,WAAAA,MAAA;YAAA,OAAAtB,KAAA,CAAAW,kBAAA,CAAAX,KAAA,CAAAyB,sBAAA;UAAA;UACAF,KAAA;UACAV,GAAA;UACAC,KAAA,WAAAA,MAAA;YACAd,KAAA,CAAAe,YAAA;YACAf,KAAA,CAAAK,kBAAA,CAAAqB,QAAA;YACA1B,KAAA,CAAAI,WAAA,OAAAc,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAnB,KAAA,CAAAG,cAAA,GAAAH,KAAA,CAAAK,kBAAA;UACA;QACA;MAEA,GACA;QACAI,KAAA;QACAC,KAAA,WAAAA,MAAA;UAAA,OAAAV,KAAA,CAAAW,kBAAA,CAAAX,KAAA,CAAA2B,sBAAA;QAAA;QACAd,GAAA;QACAC,KAAA,WAAAA,MAAA;UACAd,KAAA,CAAAe,YAAA;UACAf,KAAA,CAAAI,WAAA,OAAAc,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAnB,KAAA,CAAAG,cAAA,GAAAH,KAAA,CAAAK,kBAAA;QACA;QACAe,MAAA,GACA;UACAC,IAAA,EAAA/C,OAAA;UACAgD,KAAA,WAAAA,MAAA;YAAA,OAAAtB,KAAA,CAAAW,kBAAA,CAAAX,KAAA,CAAA2B,sBAAA;UAAA;UACAJ,KAAA;UACAV,GAAA;UACAC,KAAA,WAAAA,MAAA;YACAd,KAAA,CAAAE,cAAA;YACAF,KAAA,CAAAC,UAAA;YACAD,KAAA,CAAAe,YAAA;UACA;QACA,GACA;UACAM,IAAA,EAAA/C,OAAA;UACAgD,KAAA,WAAAA,MAAA;YAAA,OAAAtB,KAAA,CAAAW,kBAAA,CAAAX,KAAA,CAAA2B,sBAAA;UAAA;UACAJ,KAAA;UACAV,GAAA;UACAC,KAAA,WAAAA,MAAA;YACAd,KAAA,CAAAE,cAAA;YACAF,KAAA,CAAAC,UAAA;YACAD,KAAA,CAAA4B,YAAA;YACA5B,KAAA,CAAAe,YAAA;UACA;QACA;MAEA,EACA;MACAc,2BAAA;MACAC,yBAAA;MACAN,2BAAA;MACAG,sBAAA;MACAF,sBAAA;IACA;EACA;EACAM,KAAA;IACAC,MAAA;MACAC,OAAA,WAAAA,QAAAC,MAAA;QAAA,IAAAC,MAAA;QACA,IAAAD,MAAA,CAAAE,KAAA,CAAAC,IAAA;UACA;UACA,KAAA/B,WAAA;UACA,KAAAC,UAAA;;UAEA;UACA,KAAA+B,SAAA;YACA;YACAH,MAAA,CAAA3B,eAAA,IAAAY,MAAA,IAAAN,KAAA;UACA;QACA;QACA,IAAAoB,MAAA,CAAAE,KAAA,CAAAC,IAAA;UACA,KAAA/B,WAAA;UACA,KAAAC,UAAA;QACA;MACA;MACAgC,SAAA;IACA;IACAjC,WAAA;MACA2B,OAAA,WAAAA,QAAAC,MAAA;QACA;QACA;QACA;QACA;QACA;MAAA;IAEA;IACAjC,UAAA;MACAgC,OAAA,WAAAA,QAAAC,MAAA;QACA,SAAA5B,WAAA;UACA,IAAA4B,MAAA;YACA,KAAA3B,UAAA;UACA;UACA,IAAA2B,MAAA;YACA,KAAA3B,UAAA;UACA;QACA;MACA;IACA;EACA;EACAiC,OAAA,WAAAA,QAAA;IACA,IAAAJ,KAAA,QAAAJ,MAAA,CAAAI,KAAA;IACA,IAAAA,KAAA;MACA,KAAAjC,cAAA,GAAAiC,KAAA;MACA,KAAAhC,WAAA,OAAAc,cAAA,CAAAC,OAAA,WAAAhB,cAAA;MACA,IAAAiC,KAAA,CAAAK,IAAA;QACA,KAAAvC,cAAA,GAAAkC,KAAA,CAAAK,IAAA;QACA,KAAAxC,UAAA,GAAAmC,KAAA,CAAAK,IAAA;MACA;IACA;IACA,IAAAC,MAAA;MACAC,SAAA,MAAAC,gBAAA,MAAAC,IAAA,GAAAC,QAAA;MACAC,OAAA,MAAAH,gBAAA,MAAAC,IAAA,GAAAC,QAAA;MACAE,WAAA;IACA;IACA,KAAAC,uBAAA,CAAAP,MAAA;IACA,KAAAQ,+BAAA,CAAAR,MAAA;IACA,KAAAS,qBAAA;IACA,KAAAC,2BAAA;MACAV,MAAA;QACAW,SAAA,EAAAX,MAAA,CAAAC,SAAA;QACAI,OAAA,EAAAL,MAAA,CAAAK;MACA;MACAC,WAAA,EAAAN,MAAA,CAAAM;IACA;EACA;EACAM,QAAA;IACA1C,sBAAA,WAAAA,uBAAA;MACA,YAAAX,UAAA,qBAAA6B,yBAAA,QAAAD,2BAAA;IACA;EACA;EACA0B,OAAA;IACAN,uBAAA,WAAAA,wBAAAP,MAAA;MAAA,IAAAc,MAAA;MACA,IAAAC,iCAAA,EAAAf,MAAA,EAAAgB,IAAA,WAAAC,GAAA;QACAH,MAAA,CAAA3B,2BAAA,GAAA8B,GAAA,CAAA5D,IAAA;MACA;IACA;IACA6D,6BAAA,WAAAA,8BAAAlB,MAAA;MAAA,IAAAmB,MAAA;MACA,IAAAC,sCAAA,EAAApB,MAAA,EAAAgB,IAAA,WAAAC,GAAA;QACAE,MAAA,CAAA/B,yBAAA,GAAA6B,GAAA,CAAA5D,IAAA;MACA;IACA;IACAmD,+BAAA,WAAAA,gCAAAR,MAAA;MAAA,IAAAqB,MAAA;MACA,IAAAC,yCAAA,EAAAtB,MAAA,EAAAgB,IAAA,WAAAC,GAAA;QACAI,MAAA,CAAAvC,2BAAA,GAAAmC,GAAA,CAAA5D,IAAA;MACA;IACA;IACAoD,qBAAA,WAAAA,sBAAAT,MAAA;MAAA,IAAAuB,MAAA;MACA,IAAAd,6CAAA,EAAAT,MAAA,EAAAgB,IAAA,WAAAC,GAAA;QACAM,MAAA,CAAAtC,sBAAA,GAAAgC,GAAA,CAAA5D,IAAA;MACA;IACA;IACAqD,2BAAA,WAAAA,4BAAAV,MAAA;MAAA,IAAAwB,MAAA;MACA,IAAAd,2CAAA,EAAAV,MAAA,EAAAgB,IAAA,WAAAC,GAAA;QACAO,MAAA,CAAAzC,sBAAA,GAAAkC,GAAA,CAAA5D,IAAA;MACA;IACA;IACAoE,WAAA,WAAAA,YAAA;MACA,KAAAjE,cAAA,QAAAD,UAAA;MACA,KAAAmE,OAAA,CAAAC,IAAA;QAAAjC,KAAA;MAAA;IACA;IACArB,YAAA,WAAAA,aAAAuD,OAAA,EAAAC,MAAA;MACA,SAAAjE,WAAA,KAAAgE,OAAA;QACA,KAAA/D,UAAA;MACA;MACA,KAAAF,kBAAA;;MAEA;MACA,IAAAiE,OAAA;QACA;QACA,SAAAhE,WAAA,KAAAgE,OAAA;UACA,KAAArE,UAAA;UACA,KAAAC,cAAA;QACA;MACA,WAAAoE,OAAA;QACA;QACA,SAAAhE,WAAA,KAAAgE,OAAA;UACA,KAAArE,UAAA;UACA,KAAAC,cAAA;QACA;MACA;MACA,KAAAI,WAAA,GAAAgE,OAAA;MACA,KAAA/D,UAAA,GAAAgE,MAAA;MACA;MACA;IACA;IACA5D,kBAAA,WAAAA,mBAAA6D,OAAA,EAAA3D,GAAA;MACA,KAAA2D,OAAA;QACA;MACA;MACA,OAAAA,OAAA,CAAA3D,GAAA;IACA;IACA4D,iBAAA,WAAAA,kBAAA;MACA;MACA,SAAAnE,WAAA;QACA,KAAAC,UAAA;QACA;QACA,KAAAF,kBAAA,CAAAqB,QAAA;QACA,KAAAtB,WAAA,OAAAc,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,WAAAhB,cAAA,QAAAE,kBAAA;MACA;IACA;IACAqE,oBAAA,WAAAA,qBAAAC,WAAA;MACA;MACA,IAAAA,WAAA,CAAAjD,QAAA;QACA;QACA,QAAAiD,WAAA,CAAAjD,QAAA;UACA;YACA,KAAAnB,UAAA;YACA;UACA;YACA,KAAAA,UAAA;YACA;UACA;YACA,KAAAA,UAAA;YACA;UACA;YACA,KAAAA,UAAA;QACA;MACA;QACA,KAAAA,UAAA;MACA;IACA;IACAqE,kBAAA,WAAAA,mBAAAlC,MAAA;MACA,KAAAA,MAAA,CAAA1B,UAAA;QACA,KAAAT,UAAA;MACA;MACAmC,MAAA,CAAA1B,UAAA;MACA,KAAAiC,uBAAA,CAAAP,MAAA;IACA;IACAmC,wBAAA,WAAAA,yBAAAnC,MAAA;MACA,KAAAA,MAAA,CAAAzB,SAAA;QACA,KAAAV,UAAA;MACA;MACAmC,MAAA,CAAAzB,SAAA;MACA,KAAA2C,6BAAA,CAAAlB,MAAA;IACA;IACAoC,uBAAA,WAAAA,wBAAApC,MAAA;MACA,KAAAA,MAAA,CAAA1B,UAAA;QACA,KAAAT,UAAA;MACA;MACAmC,MAAA,CAAA1B,UAAA;MACA,KAAAkC,+BAAA,CAAAR,MAAA;IACA;IACAqC,qBAAA,WAAAA,sBAAArC,MAAA;MACA;MACA,IAAAsC,gBAAA;QACAtC,MAAA;UACAW,SAAA,EAAAX,MAAA,CAAAA,MAAA,GAAAA,MAAA,CAAAA,MAAA,CAAAW,SAAA,GAAA4B,SAAA;UACAlC,OAAA,EAAAL,MAAA,CAAAA,MAAA,GAAAA,MAAA,CAAAA,MAAA,CAAAK,OAAA,GAAAkC;QACA;QACAC,cAAA,EAAAxC,MAAA,CAAAwC;MACA;MACA;MACA,IAAAxC,MAAA,CAAAyC,UAAA;QACAH,gBAAA,CAAAG,UAAA,GAAAzC,MAAA,CAAAyC,UAAA;MACA;MAEA,IAAAzC,MAAA,CAAAM,WAAA,KAAAiC,SAAA,IAAAvC,MAAA,CAAAM,WAAA;QACAgC,gBAAA,CAAAhC,WAAA,GAAAN,MAAA,CAAAM,WAAA;MACA;MAEAN,MAAA,CAAAhB,QAAA;MACA,KAAA0B,2BAAA,CAAA4B,gBAAA;IACA;IACAI,qBAAA,WAAAA,sBAAA1C,MAAA;MACA,KAAAS,qBAAA,CAAAT,MAAA;IACA;EACA;AACA", "ignoreList": []}]}