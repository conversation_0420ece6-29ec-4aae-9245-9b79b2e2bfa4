<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.ffsafe.api.mapper.FfsafeHostIntrusionAttackDetailMapper">
    
    <resultMap type="FfsafeHostIntrusionAttackDetail" id="FfsafeHostIntrusionAttackDetailResult">
        <result property="id" column="id" />
        <result property="attackId" column="attack_id" />
        <result property="detailType" column="detail_type" />
        <result property="detailData" column="detail_data" />
        <result property="createTime" column="create_time" />
    </resultMap>

    <sql id="selectFfsafeHostIntrusionAttackDetailVo">
        select id, attack_id, detail_type, detail_data, create_time
        from ffsafe_host_intrusion_attack_detail
    </sql>

    <select id="selectFfsafeHostIntrusionAttackDetailList" parameterType="FfsafeHostIntrusionAttackDetail" resultMap="FfsafeHostIntrusionAttackDetailResult">
        <include refid="selectFfsafeHostIntrusionAttackDetailVo"/>
        <where>  
            <if test="attackId != null "> and attack_id = #{attackId}</if>
            <if test="detailType != null  and detailType != ''"> and detail_type = #{detailType}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectFfsafeHostIntrusionAttackDetailById" parameterType="Long" resultMap="FfsafeHostIntrusionAttackDetailResult">
        <include refid="selectFfsafeHostIntrusionAttackDetailVo"/>
        where id = #{id}
    </select>

    <select id="selectByAttackId" parameterType="Long" resultMap="FfsafeHostIntrusionAttackDetailResult">
        <include refid="selectFfsafeHostIntrusionAttackDetailVo"/>
        where attack_id = #{attackId}
        order by create_time desc
    </select>
        
    <insert id="insertFfsafeHostIntrusionAttackDetail" parameterType="FfsafeHostIntrusionAttackDetail" useGeneratedKeys="true" keyProperty="id">
        insert into ffsafe_host_intrusion_attack_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="attackId != null">attack_id,</if>
            <if test="detailType != null and detailType != ''">detail_type,</if>
            <if test="detailData != null and detailData != ''">detail_data,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="attackId != null">#{attackId},</if>
            <if test="detailType != null and detailType != ''">#{detailType},</if>
            <if test="detailData != null and detailData != ''">#{detailData},</if>
         </trim>
    </insert>

    <update id="updateFfsafeHostIntrusionAttackDetail" parameterType="FfsafeHostIntrusionAttackDetail">
        update ffsafe_host_intrusion_attack_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="attackId != null">attack_id = #{attackId},</if>
            <if test="detailType != null and detailType != ''">detail_type = #{detailType},</if>
            <if test="detailData != null and detailData != ''">detail_data = #{detailData},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFfsafeHostIntrusionAttackDetailById" parameterType="Long">
        delete from ffsafe_host_intrusion_attack_detail where id = #{id}
    </delete>

    <delete id="deleteFfsafeHostIntrusionAttackDetailByIds" parameterType="String">
        delete from ffsafe_host_intrusion_attack_detail where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteFfsafeHostIntrusionAttackDetailByAttackId" parameterType="Long">
        delete from ffsafe_host_intrusion_attack_detail where attack_id = #{attackId}
    </delete>

    <delete id="batchDeleteByAttackIds">
        delete from ffsafe_host_intrusion_attack_detail where attack_id in
        <foreach collection="attackIds" item="attackId" open="(" separator="," close=")">
            #{attackId}
        </foreach>
    </delete>

    <insert id="batchInsertFfsafeHostIntrusionAttackDetail" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into ffsafe_host_intrusion_attack_detail (attack_id, detail_type, detail_data)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.attackId}, #{item.detailType}, #{item.detailData})
        </foreach>
    </insert>

    <select id="selectByAttackIds" parameterType="java.util.List" resultMap="FfsafeHostIntrusionAttackDetailResult">
        <include refid="selectFfsafeHostIntrusionAttackDetailVo"/>
        where attack_id in
        <foreach collection="attackIds" item="attackId" open="(" separator="," close=")">
            #{attackId}
        </foreach>
    </select>

    <!-- 批量更新详情方法：在ExecutorType.BATCH模式下，MyBatis会为每个item创建单独的PreparedStatement -->
    <!-- 因此不需要使用separator，每个update语句会被单独执行 -->
    <update id="batchUpdateFfsafeHostIntrusionAttackDetail" parameterType="FfsafeHostIntrusionAttackDetail">
        update ffsafe_host_intrusion_attack_detail
        <set>
            <if test="detailType != null">detail_type = #{detailType},</if>
            <if test="detailData != null">detail_data = #{detailData},</if>
        </set>
        where id = #{id}
    </update>

</mapper>
