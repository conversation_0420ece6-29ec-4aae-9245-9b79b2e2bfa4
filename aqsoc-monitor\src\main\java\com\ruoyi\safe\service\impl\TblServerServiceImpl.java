package com.ruoyi.safe.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.domain.TreeSelect;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.CollectionUtils;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanValidators;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.dict.domain.NetworkDomain;
import com.ruoyi.dict.domain.TblLocation;
import com.ruoyi.dict.service.INetworkDomainService;
import com.ruoyi.dict.service.ITblLocationService;
import com.ruoyi.ffsafe.scantaskapi.domain.EdrDetailParam;
import com.ruoyi.ffsafe.scantaskapi.domain.EdrDetailsResult;
import com.ruoyi.monitor2.changting.client.SkipHttpsUtil;
import com.ruoyi.monitor2.domain.TblProduct;
import com.ruoyi.monitor2.service.ITblProductService;
import com.ruoyi.safe.countByDict.service.ICountByDictService;
import com.ruoyi.safe.domain.*;
import com.ruoyi.safe.domain.dto.QueryDeptServerCountDto;
import com.ruoyi.safe.dto.TblServerImportDTO;
import com.ruoyi.safe.mapper.MonitorHandleMapper;
import com.ruoyi.safe.mapper.TblDeployMapper;
import com.ruoyi.safe.mapper.TblServerMapper;
import com.ruoyi.safe.service.*;
import com.ruoyi.common.utils.bean.BeanUtil;
import com.ruoyi.safe.task.AssetOnlineTask;
import com.ruoyi.safe.vo.DeptServerCount;
import com.ruoyi.safe.vo.countDict.CountByDictVO;
import com.ruoyi.safe.vo.countDict.CountDictTypeVO;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.security.web.util.matcher.IpAddressMatcher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.Validator;
import java.io.IOException;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 服务器/存储设备Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-11-09
 */
@Slf4j
@Service
public class TblServerServiceImpl implements ITblServerService {
    @Resource
    private TblServerMapper tblServerMapper;
    @Autowired
    private ITblAssetOverviewService tblAssetOverviewService;
    @Autowired
    private com.ruoyi.safe.mapper.TblAssetOverviewMapper tblAssetOverviewMapper;
    @Autowired
    private TblDeployServiceImpl deployService;
    @Resource
    private TblDeployMapper deployMapper;
    @Autowired
    private Snowflake snowflake;
    @Autowired
    private INetworkDomainService networkDomainService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private ISysDeptService sysDeptService;
    @Autowired
    private ITblDeployService tblDeployService;
    @Autowired
    private ITblVendorService tblVendorService;
    @Autowired
    private ITblLocationService tblLocationService;
    @Autowired
    private ITblNetworkIpMacService tblNetworkIpMacService;
    @Autowired
    private ITblProductService tblProductService;
    @Autowired
    private Validator validator;
    @Resource
    private MonitorHandleMapper monitorHandleMapper;
    @Autowired
    private AssetOnlineTask assetOnlineTask;
    @Autowired
    private ISysDictDataService dictDataService;
    @Autowired
    private ICountByDictService countByDictService;

    @Autowired
    private EdrDetailParam edrDetailParam;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private ITblBusinessApplicationService tblBusinessApplicationService;

    @Autowired
    private ITblApplicationServerService tblApplicationServerService;

    /**
     * 查询服务器/存储设备
     *
     * @param assetId 服务器/存储设备主键
     * @return 服务器/存储设备
     */
    @Override
    public TblServer selectTblServerByAssetId(Long assetId) {
        TblServer tblServer = tblServerMapper.selectTblServerByAssetId(assetId);
        if (ObjectUtils.isNotEmpty(tblServer)) {
            TblAssetOverview tblAssetOverview = tblAssetOverviewService.selectInfoByAssetId(assetId);
            TblNetworkIpMac net = new TblNetworkIpMac();
            net.setMainIp("1");
            net.setAssetId(assetId);
            List<TblNetworkIpMac> tblNetworkIpMacs = tblNetworkIpMacService.selectTblNetworkIpMacList(net);
            if (tblServer != null && tblAssetOverview != null) {
                BeanUtil.copyPropertiesIgnoreNull(tblAssetOverview, tblServer);
                if (ObjectUtils.isNotEmpty(tblNetworkIpMacs)) {
                    net = tblNetworkIpMacs.get(0);
                    tblServer.setIp(net.getIpv4());
                    tblServer.setMac(net.getMac());
                    tblServer.setDomainId(net.getDomainId());
                }
            }

            TblNetworkIpMac net1 = new TblNetworkIpMac();
            net1.setAssetId(assetId);
            tblServer.setIpMacArr(tblNetworkIpMacService.selectTblNetworkIpMacList(net1));

            if (ObjectUtils.isNotEmpty(tblAssetOverview)) {
                NetworkDomain domain = networkDomainService.selectNetworkDomainByDomainId(tblAssetOverview.getDomainId());
                if (ObjectUtils.isNotEmpty(domain)) {
                    tblServer.setDomainName(domain.getDomainName());
                }
            }
            if (ObjectUtils.isNotEmpty(tblServer.getOptsystem())) {
                tblServer.getOptsystem().setProduct(tblProductService.selectTblProductByPrdid(tblServer.getOptsystem().getPrdid()));
            }
            if (ObjectUtils.isNotEmpty(tblServer.getDbsystem())) {
                tblServer.getDbsystem().setProduct(tblProductService.selectTblProductByPrdid(tblServer.getDbsystem().getPrdid()));
            }
            // 查询中间件和数据库
            List<TblDeploy> tblDeployList = tblDeployService.selectTblDeployByAssetIds(Arrays.asList(assetId), new TblDeploy(), false);
            tblServer.setDbsystemArr(new ArrayList<>());
            tblServer.setMdsystemArr(new ArrayList<>());
            for (TblDeploy tblDeploy : tblDeployList) {
                if (!StringUtils.isEmpty(tblDeploy.getSoftlx())) {
                    if (tblDeploy.getSoftlx().equals("2")) {
                        tblServer.getDbsystemArr().add(tblDeploy);
                    } else if (tblDeploy.getSoftlx().equals("3")) {
                        tblServer.getMdsystemArr().add(tblDeploy);
                    }
                }
            }
        }


        return tblServer;
    }

    /**
     * 查询服务器/存储设备列表
     *
     * @param assetIds 服务器/存储设备主键
     * @return 服务器/存储设备集合
     */
    @Override
    public List<TblServer> selectTblServerByAssetIds(Long[] assetIds) {
        List<TblServer> assetList = tblServerMapper.selectTblServerByAssetIds(assetIds);
        List<TblAssetOverview> tblAssetOverviews = tblAssetOverviewService.selectInfoByAssetIds(assetIds);
        CollectionUtils.associationOneToOne(
                assetList,
                tblAssetOverviews,
                TblServer::getAssetId,
                TblAssetOverview::getAssetId,
                (asset, assetOverview) -> {
                    BeanUtil.copyPropertiesIgnoreNull(assetOverview, asset);
                }
        );
        Optional.ofNullable(assetList.stream().map(asset -> asset.getDomainId()).toArray(Long[]::new))
                .map(ids -> {
                    if (ObjectUtils.isNotEmpty(ids))
                        return networkDomainService.selectNetworkDomainByDomainIds(ids, new NetworkDomain());
                    return null;
                })
                .ifPresent(domainList -> {
                    CollectionUtils.associationOneToOne(
                            assetList, domainList,
                            TblServer::getDomainId, NetworkDomain::getDomainId,
                            (asset, domain) -> {
                                asset.setDomainName(domain.getDomainName());
                            }
                    );
                });
        return assetList;
    }

    /**
     * 查询服务器/存储设备列表
     *
     * @param tblServer 服务器/存储设备
     * @return 服务器/存储设备
     */
    @Override
    public List<TblServer> selectTblServerList(TblServer tblServer) {
        List<TblServer> assetList = tblServerMapper.selectTblServerList(tblServer);
        Long[] assetIds = CollectionUtils.getListAndDeWeight(assetList, TblServer::getAssetId).toArray(new Long[0]);
        if (assetIds.length != 0) {
            List<TblAssetOverview> tblAssetOverviews = tblAssetOverviewService.selectInfoByAssetIds(assetIds);
            CollectionUtils.associationOneToOne(
                    assetList,
                    tblAssetOverviews,
                    TblServer::getAssetId,
                    TblAssetOverview::getAssetId,
                    (asset, assetOverview) -> {
                        BeanUtil.copyPropertiesIgnoreNull(assetOverview, asset);
                    }
            );
        }
        Optional.ofNullable(assetList.stream().map(asset -> asset.getDomainId()).toArray(Long[]::new))
                .map(ids -> {
                    if (ObjectUtils.isNotEmpty(ids))
                        return networkDomainService.selectNetworkDomainByDomainIds(ids, new NetworkDomain());
                    return null;
                })
                .ifPresent(domainList -> {
                    CollectionUtils.associationOneToOne(
                            assetList, domainList,
                            TblServer::getDomainId, NetworkDomain::getDomainId,
                            (asset, domain) -> {
                                asset.setDomainName(domain.getDomainName());
                            }
                    );
                });
        return assetList;
    }

    @Override
    public List<TblServer> selectTblServerList2(TblServer tblServer) {
        if(StrUtil.isNotBlank(tblServer.getDomainIds())){
            tblServer.setDomainIdsList(Arrays.asList(tblServer.getDomainIds().split(",")));
        }
        if (StringUtils.isNotEmpty(tblServer.getDeptStr())){
            tblServer.setDeptIds(Arrays.asList(tblServer.getDeptStr().split(",")));
        }
        List<TblServer> tblServers = tblServerMapper.selectTblServerList(tblServer);
        List<TblVendor> vendors = new ArrayList<>();
        List<SysUser> users = new ArrayList<>();
        List<SysDept> depts = new ArrayList<>();
        List<TblDeploy> deplx1 = new ArrayList<>();
        List<TblDeploy> deplx2 = new ArrayList<>();
        List<TblDeploy> deplx3 = new ArrayList<>();
        List<NetworkDomain> domains = new ArrayList<>();
//        List<TblAssetOverview> assetOverviews = new ArrayList<>();
//        List<TblLocation> locations  =new ArrayList<>();
        if (ObjectUtils.isNotEmpty(tblServers)) {
            List<Long> vendorIds = new ArrayList<>();
            List<Long> userIds = new ArrayList<>();
            List<Long> deptIds = new ArrayList<>();
            List<Long> assetIds = new ArrayList<>();
            List<Long> domainIds = new ArrayList<>();
//            List<Long> locationIds = new ArrayList<>();
            for (TblServer server : tblServers) {
                if (ObjectUtils.isNotEmpty(server.getVendor()))
                    vendorIds.add(server.getVendor());
                if (ObjectUtils.isNotEmpty(server.getUserId()))
                    userIds.add(server.getUserId());
                if (ObjectUtils.isNotEmpty(server.getDeptId()))
                    deptIds.add(server.getDeptId());
                if (ObjectUtils.isNotEmpty(server.getDomainId()))
                    domainIds.add(server.getDomainId());
                assetIds.add(server.getAssetId());
                // 查询中间件和数据库
                List<TblDeploy> tblDeployList = tblDeployService.selectTblDeployByAssetIds(Arrays.asList(server.getAssetId()), new TblDeploy(), false);
                server.setDbsystemArr(new ArrayList<>());
                server.setMdsystemArr(new ArrayList<>());
                for (TblDeploy tblDeploy : tblDeployList) {
                    if (!StringUtils.isEmpty(tblDeploy.getSoftlx())) {
                        if (tblDeploy.getSoftlx().equals("2")) {
                            server.getDbsystemArr().add(tblDeploy);
                        } else if (tblDeploy.getSoftlx().equals("3")) {
                            server.getMdsystemArr().add(tblDeploy);
                        }
                    }
                }
                // 获取网络信息
                TblNetworkIpMac net = new TblNetworkIpMac();
                net.setAssetId(server.getAssetId());
                server.setIpMacArr(tblNetworkIpMacService.selectTblNetworkIpMacList(net));
            }
            if (ObjectUtils.isNotEmpty(vendorIds)) {
                vendors = (tblVendorService.selectTblVendorByIds(vendorIds));
            }
            if (ObjectUtils.isNotEmpty(userIds)) {
                users = (sysUserService.selectUserByIds(userIds));
            }
            if (ObjectUtils.isNotEmpty(deptIds)) {
                depts = (sysDeptService.selectDeptByIds(deptIds));
            }
            if (ObjectUtils.isNotEmpty(domainIds)) {
                domains = networkDomainService.selectNetworkDomainByDomainIds(domainIds.toArray(new Long[]{}), new NetworkDomain());
            }
            if (ObjectUtils.isNotEmpty(assetIds)) {
                TblDeploy deploy = new TblDeploy();
                deploy.setSoftlx("1");
                deplx1 = (tblDeployService.selectTblDeployByAssetIds(assetIds, deploy, true));
                deploy.setSoftlx("2");
                deplx2 = (tblDeployService.selectTblDeployByAssetIds(assetIds, deploy, true));
                deploy.setSoftlx("3");
                deplx3 = (tblDeployService.selectTblDeployByAssetIds(assetIds, deploy, true));
//                assetOverviews = tblAssetOverviewService.selectInfoByAssetIds(assetIds.toArray(new Long[0]));
//                locationIds = assetOverviews.stream().map(asset->asset.getLocationId()).collect(Collectors.toList());
//                if(ObjectUtils.isNotEmpty(locationIds)){
//                    locations = tblLocationService.selectTblLocationByLocationIds(locationIds);
//                }
            }
            CollectionUtils.associationOneMapMany(tblServers, new CollectionUtils.Polymerization[]{
                    CollectionUtils.getPolymerization(tblServers, vendors, TblServer::getVendor, TblVendor::getId, (server, vendor) -> {
                        server.setVendorName(vendor.getVendorName());
                    }),
                    CollectionUtils.getPolymerization(tblServers, users, TblServer::getUserId, SysUser::getUserId, (server, user) -> {
                        server.setUserName(user.getUserName());
                    }),
                    CollectionUtils.getPolymerization(tblServers, depts, TblServer::getDeptId, SysDept::getDeptId, (server, dept) -> {
                        server.setDeptName(dept.getDeptName());
                    }),
                    CollectionUtils.getPolymerization(tblServers, deplx1, TblServer::getAssetId, TblDeploy::getAssetId, (server, deploy) -> {
                        server.setOptsystem(deploy);
                    }),
                    CollectionUtils.getPolymerization(tblServers, deplx2, TblServer::getAssetId, TblDeploy::getAssetId, (server, deploy) -> {
                        server.setDbsystem(deploy);
                    }),
                    CollectionUtils.getPolymerization(tblServers, deplx3, TblServer::getAssetId, TblDeploy::getAssetId, (server, deploy) -> {
                        server.setMdsystem(deploy);
                    }),
                    CollectionUtils.getPolymerization(tblServers, domains, TblServer::getDomainId, NetworkDomain::getDomainId, (server, domain) -> {
                        server.setDomainName(domain.getDomainFullName());
                    }),
//                    CollectionUtils.getPolymerization(tblServers,assetOverviews,TblServer::getAssetId,TblAssetOverview::getAssetId,(server,asset)->{
//                        server.setState(asset.getState());
//                        server.setLocationId(asset.getLocationId());
//                        server.setLaseScanState(asset.getLaseScanState());
//                        server.setVulnNum(asset.getVulnNum());
//                        server.setLastScanTime(asset.getLastScanTime());
//                        server.setVnlnUpdateTime(asset.getVnlnUpdateTime());
//                    }),
//                    CollectionUtils.getPolymerization(tblServers,locations,TblServer::getLocationId,TblLocation::getLocationId,(server,location)->{
//                        server.setLocationFullName(location.getLocationFullName());
//                    })
            });

        }
        ;
        return tblServers;
    }

    @Override
    public int countNum() {
        return tblServerMapper.countNum();
    }

    /**
     * 新增服务器/存储设备
     *
     * @param tblServer 服务器/存储设备
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertTblServer(TblServer tblServer) {
        if (tblServer.getHandleId() != null) {
            monitorHandleMapper.deleteMonitorHandleById(tblServer.getHandleId());
        }
        tblServer.setAssetId(snowflake.nextId());
        tblServer.setCreateTime(DateUtils.getNowDate());
        if (ObjectUtils.isEmpty(tblServer.getAssetClass())) {
            tblServer.setAssetClass(4L);
        }
        tblAssetOverviewService.insertTblAssetOverview(tblServer);
        deployMapper.deleteDeployByAssetId(tblServer.getAssetId());
        if (tblServer.getOptsystem() != null) {
            TblDeploy deploy = tblServer.getOptsystem();
            deploy.setAssetId(tblServer.getAssetId());
            deploy.setAssetName(tblServer.getAssetName());
            deploy.setSoftlx("1");
            if (StringUtils.isNotEmpty(deploy.getPrdid()))
                deployService.insertTblDeploy(deploy);
        }
        if (tblServer.getDbsystem() != null) {
            TblDeploy deploy = tblServer.getDbsystem();
            deploy.setAssetId(tblServer.getAssetId());
            deploy.setAssetName(tblServer.getAssetName());
            deploy.setSoftlx("2");
            if (StringUtils.isNotEmpty(deploy.getPrdid()))
                deployService.insertTblDeploy(deploy);
        }
        if (tblServer.getMdsystem() != null) {
            TblDeploy deploy = tblServer.getMdsystem();
            deploy.setAssetId(tblServer.getAssetId());
            deploy.setAssetName(tblServer.getAssetName());
            deploy.setSoftlx("3");
            if (StringUtils.isNotEmpty(deploy.getPrdid()))
                deployService.insertTblDeploy(deploy);
        }
        TblNetworkIpMac tblNetworkIpMac = new TblNetworkIpMac();
        tblNetworkIpMac.setAssetId(tblServer.getAssetId());
        tblNetworkIpMac.setMainIp("1");
        List<TblNetworkIpMac> tblNetworkIpMacs = tblNetworkIpMacService.selectTblNetworkIpMacList(tblNetworkIpMac);
        if (ObjectUtils.isNotEmpty(tblServer.getIp()) || ObjectUtils.isNotEmpty(tblServer.getDomainId())) {
            if (ObjectUtils.isNotEmpty(tblNetworkIpMacs)) {
                TblNetworkIpMac mainIp = tblNetworkIpMacs.get(0);
                mainIp.setIpv4(tblServer.getIp());
                mainIp.setDomainId(tblServer.getDomainId());
                mainIp.setMac(tblServer.getMac());
                tblNetworkIpMacService.updateTblNetworkIpMac(mainIp);
            } else {
                tblNetworkIpMac.setIpv4(tblServer.getIp());
                tblNetworkIpMac.setDomainId(tblServer.getDomainId());
                tblNetworkIpMac.setMac(tblServer.getMac());
                tblNetworkIpMacService.insertTblNetworkIpMac(tblNetworkIpMac);
            }
        } else {
            tblNetworkIpMac.setDomainId(tblServer.getDomainId());
            tblNetworkIpMacService.updateTblNetworkIpMac(tblNetworkIpMac);
        }
        tblServer.setFacilityManufacturer(tblServer.getFacilityManufacturer());
        tblServer.setMaintainUnit(tblServer.getMaintainUnit());
        int i = tblServerMapper.insertTblServer(tblServer);
        //在线检测
        if(StrUtil.isNotBlank(tblServer.getIp())){
            List<JSONObject> assetList = new ArrayList<>(1);
            JSONObject assetInfo = new JSONObject();
            assetInfo.put("ip",tblServer.getIp());
            assetInfo.put("assetId",tblServer.getAssetId());
            assetList.add(assetInfo);
            assetOnlineTask.asyncAssetOnlineCheck(assetList);
        }
        return i;
    }

    /**
     * 新增服务器/存储设备
     *
     * @param tblServer 服务器/存储设备
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertTblServer2(TblServer tblServer) {
        /*if (StrUtil.isEmpty(tblServer.getAssetCode())){
            CountDictTypeVO countByDict = countByDictService.getCountByDict("asset_type","server");
            if (ObjectUtils.isNotEmpty(countByDict)){
                if (CollUtil.isNotEmpty(countByDict.getCountByDictList())){
                    List<CountByDictVO> countByDictList = countByDict.getCountByDictList();
                    countByDictList.stream().filter(countByDictItem ->
                            countByDictItem.getDictValue().equals(tblServer.getAssetType().toString())).findFirst().ifPresent(countByDictVO ->
                            tblServer.setAssetCode(countByDictVO.getDictValue() + DateUtil.format(DateUtil.date(), "yyyyMMddHHmmss")));
                }
            }
        }*/
        if (tblServer.getHandleId() != null) {
            monitorHandleMapper.deleteMonitorHandleById(tblServer.getHandleId());
        }
        tblServer.setAssetId(snowflake.nextId());
        tblServer.setCreateTime(DateUtils.getNowDate());
        if (ObjectUtils.isEmpty(tblServer.getAssetClass())) {
            tblServer.setAssetClass(4L);
        }
        tblAssetOverviewService.insertTblAssetOverview(tblServer);
        deployMapper.deleteDeployByAssetId(tblServer.getAssetId());
        if (tblServer.getOptsystem() != null) {
            TblDeploy deploy = tblServer.getOptsystem();
            deploy.setAssetId(tblServer.getAssetId());
            deploy.setAssetName(tblServer.getAssetName());
            deploy.setSoftlx("1");
            if (StringUtils.isNotEmpty(deploy.getPrdid()))
                deployService.insertTblDeploy(deploy);
        }
        if (!CollectionUtils.isEmpty(tblServer.getDbsystemArr())) {
            for (TblDeploy deploy : tblServer.getDbsystemArr()) {
                deploy.setAssetId(tblServer.getAssetId());
                deploy.setAssetName(tblServer.getAssetName());
                deploy.setSoftlx("2");
                if (StringUtils.isNotEmpty(deploy.getPrdid()))
                    deployService.insertTblDeploy(deploy);
            }
        }
        if (!CollectionUtils.isEmpty(tblServer.getMdsystemArr())) {
            for (TblDeploy deploy : tblServer.getMdsystemArr()) {
                deploy.setAssetId(tblServer.getAssetId());
                deploy.setAssetName(tblServer.getAssetName());
                deploy.setSoftlx("3");
                if (StringUtils.isNotEmpty(deploy.getPrdid()))
                    deployService.insertTblDeploy(deploy);
            }
        }
//        TblNetworkIpMac tblNetworkIpMac = new TblNetworkIpMac();
//        tblNetworkIpMac.setAssetId(tblServer.getAssetId());
//        tblNetworkIpMac.setMainIp("1");
//        List<TblNetworkIpMac> tblNetworkIpMacs = tblNetworkIpMacService.selectTblNetworkIpMacList(tblNetworkIpMac);
//        if(ObjectUtils.isNotEmpty(tblServer.getIp())||ObjectUtils.isNotEmpty(tblServer.getDomainId())){
//            if(ObjectUtils.isNotEmpty(tblNetworkIpMacs)){
//                TblNetworkIpMac mainIp = tblNetworkIpMacs.get(0);
//                mainIp.setIpv4(tblServer.getIp());
//                mainIp.setDomainId(tblServer.getDomainId());
//                mainIp.setMac(tblServer.getMac());
//                tblNetworkIpMacService.updateTblNetworkIpMac(mainIp);
//            }else{
//                tblNetworkIpMac.setIpv4(tblServer.getIp());
//                tblNetworkIpMac.setDomainId(tblServer.getDomainId());
//                tblNetworkIpMac.setMac(tblServer.getMac());
//                tblNetworkIpMacService.insertTblNetworkIpMac(tblNetworkIpMac);
//            }
//        }else{
//            tblNetworkIpMac.setDomainId(tblServer.getDomainId());
//            tblNetworkIpMacService.updateTblNetworkIpMac(tblNetworkIpMac);
//        }
        // 先删除
        tblNetworkIpMacService.deleteTblNetworkIpMacByAssetId(tblServer.getAssetId());
        // 后重新插入
        List<TblNetworkIpMac> ipMacArr = tblServer.getIpMacArr();
        for (TblNetworkIpMac ipMac : ipMacArr) {
            ipMac.setAssetId(tblServer.getAssetId());
            tblNetworkIpMacService.insertTblNetworkIpMac(ipMac);
        }


        tblServer.setFacilityManufacturer(tblServer.getFacilityManufacturer());
        tblServer.setMaintainUnit(tblServer.getMaintainUnit());
        int i = tblServerMapper.insertTblServer(tblServer);
        //在线检测
        if(StrUtil.isNotBlank(tblServer.getIp())){
            List<JSONObject> assetList = new ArrayList<>(1);
            JSONObject assetInfo = new JSONObject();
            assetInfo.put("ip",tblServer.getIp());
            assetInfo.put("assetId",tblServer.getAssetId());
            assetList.add(assetInfo);
            assetOnlineTask.asyncAssetOnlineCheck(assetList);
        }
        return i;
    }

    /**
     * 修改服务器/存储设备
     *
     * @param tblServer 服务器/存储设备
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateTblServer(TblServer tblServer) {
        if (tblServer.getHandleId() != null) {
            MonitorHandle monitorHandle = new MonitorHandle();
            monitorHandle.setId(tblServer.getHandleId());
            monitorHandle.setIsDel(1);
            monitorHandleMapper.updateMonitorHandle(monitorHandle);
        }
        tblServer.setUpdateTime(DateUtils.getNowDate());
        if (ObjectUtils.isEmpty(tblServer.getAssetClass())) {
            tblServer.setAssetClass(4L);
        }
        tblAssetOverviewService.updateTblAssetOverview(tblServer);
        deployMapper.deleteDeployByAssetId(tblServer.getAssetId());

        if (tblServer.getOptsystem() != null) {
            TblDeploy deploy = tblServer.getOptsystem();
            deploy.setAssetId(tblServer.getAssetId());
            deploy.setAssetName(tblServer.getAssetName());
            deploy.setSoftlx("1");
            if (StringUtils.isNotEmpty(deploy.getPrdid()))
                deployService.insertTblDeploy(deploy);
        }
        if (tblServer.getDbsystem() != null) {
            TblDeploy deploy = tblServer.getDbsystem();
            deploy.setAssetId(tblServer.getAssetId());
            deploy.setAssetName(tblServer.getAssetName());
            deploy.setSoftlx("2");
            if (StringUtils.isNotEmpty(deploy.getPrdid()))
                deployService.insertTblDeploy(deploy);
        }
        if (tblServer.getMdsystem() != null) {
            TblDeploy deploy = tblServer.getMdsystem();
            deploy.setAssetId(tblServer.getAssetId());
            deploy.setAssetName(tblServer.getAssetName());
            deploy.setSoftlx("3");
            if (StringUtils.isNotEmpty(deploy.getPrdid()))
                deployService.insertTblDeploy(deploy);
        }

        TblNetworkIpMac tblNetworkIpMac = new TblNetworkIpMac();
        tblNetworkIpMac.setAssetId(tblServer.getAssetId());
        tblNetworkIpMac.setMainIp("1");
        List<TblNetworkIpMac> tblNetworkIpMacs = tblNetworkIpMacService.selectTblNetworkIpMacList(tblNetworkIpMac);
        if (ObjectUtils.isNotEmpty(tblServer.getIp()) || ObjectUtils.isNotEmpty(tblServer.getDomainId())) {
            if (ObjectUtils.isNotEmpty(tblNetworkIpMacs)) {
                TblNetworkIpMac mainIp = tblNetworkIpMacs.get(0);
                mainIp.setIpv4(tblServer.getIp());
                mainIp.setDomainId(tblServer.getDomainId());
                mainIp.setMac(tblServer.getMac());
                tblNetworkIpMacService.updateTblNetworkIpMac(mainIp);
            } else {
                tblNetworkIpMac.setIpv4(tblServer.getIp());
                tblNetworkIpMac.setDomainId(tblServer.getDomainId());
                tblNetworkIpMac.setMac(tblServer.getMac());
                tblNetworkIpMacService.insertTblNetworkIpMac(tblNetworkIpMac);
            }
        } else {
            tblNetworkIpMac.setDomainId(tblServer.getDomainId());
            tblNetworkIpMacService.updateTblNetworkIpMac(tblNetworkIpMac);
        }
        int i = tblServerMapper.updateTblServer(tblServer);
        //在线检测
        if(StrUtil.isNotBlank(tblServer.getIp())){
            List<JSONObject> assetList = new ArrayList<>(1);
            JSONObject assetInfo = new JSONObject();
            assetInfo.put("ip",tblServer.getIp());
            assetInfo.put("assetId",tblServer.getAssetId());
            assetList.add(assetInfo);
            assetOnlineTask.asyncAssetOnlineCheck(assetList);
        }
        return i;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateTblServer2(TblServer tblServer) {
        if (tblServer.getHandleId() != null) {
            MonitorHandle byId = monitorHandleMapper.selectMonitorHandleById(tblServer.getHandleId());
            if (byId != null) {
                //if(byId.getMemo()!=null && byId.getMemo().startsWith(""))
                MonitorHandle monitorHandle = new MonitorHandle();
                monitorHandle.setId(tblServer.getHandleId());
                monitorHandle.setIsDel(1);
                monitorHandleMapper.updateMonitorHandle(monitorHandle);

            }
        }
        tblServer.setUpdateTime(DateUtils.getNowDate());
        if (ObjectUtils.isEmpty(tblServer.getAssetClass())) {
            tblServer.setAssetClass(4L);
        }
        tblAssetOverviewService.updateTblAssetOverview(tblServer);
        deployMapper.deleteDeployByAssetId(tblServer.getAssetId());

        if (tblServer.getOptsystem() != null) {
            TblDeploy deploy = tblServer.getOptsystem();
            deploy.setAssetId(tblServer.getAssetId());
            deploy.setAssetName(tblServer.getAssetName());
            deploy.setSoftlx("1");
            if (StringUtils.isNotEmpty(deploy.getPrdid()))
                deployService.insertTblDeploy(deploy);
        }
        if (!CollectionUtils.isEmpty(tblServer.getDbsystemArr())) {
            for (TblDeploy deploy : tblServer.getDbsystemArr()) {
                deploy.setAssetId(tblServer.getAssetId());
                deploy.setAssetName(tblServer.getAssetName());
                deploy.setSoftlx("2");
                if (StringUtils.isNotEmpty(deploy.getPrdid()))
                    deployService.insertTblDeploy(deploy);
            }
        }
        if (!CollectionUtils.isEmpty(tblServer.getMdsystemArr())) {
            for (TblDeploy deploy : tblServer.getMdsystemArr()) {
                deploy.setAssetId(tblServer.getAssetId());
                deploy.setAssetName(tblServer.getAssetName());
                deploy.setSoftlx("3");
                if (StringUtils.isNotEmpty(deploy.getPrdid()))
                    deployService.insertTblDeploy(deploy);
            }
        }

//        TblNetworkIpMac tblNetworkIpMac = new TblNetworkIpMac();
//        tblNetworkIpMac.setAssetId(tblServer.getAssetId());
//        tblNetworkIpMac.setMainIp("1");
//        List<TblNetworkIpMac> tblNetworkIpMacs = tblNetworkIpMacService.selectTblNetworkIpMacList(tblNetworkIpMac);
//        if(ObjectUtils.isNotEmpty(tblServer.getIp())||ObjectUtils.isNotEmpty(tblServer.getDomainId())){
//            if(ObjectUtils.isNotEmpty(tblNetworkIpMacs)){
//                TblNetworkIpMac mainIp = tblNetworkIpMacs.get(0);
//                mainIp.setIpv4(tblServer.getIp());
//                mainIp.setDomainId(tblServer.getDomainId());
//                mainIp.setMac(tblServer.getMac());
//                tblNetworkIpMacService.updateTblNetworkIpMac(mainIp);
//            }else{
//                tblNetworkIpMac.setIpv4(tblServer.getIp());
//                tblNetworkIpMac.setDomainId(tblServer.getDomainId());
//                tblNetworkIpMac.setMac(tblServer.getMac());
//                tblNetworkIpMacService.insertTblNetworkIpMac(tblNetworkIpMac);
//            }
//        }else{
//            tblNetworkIpMac.setDomainId(tblServer.getDomainId());
//            tblNetworkIpMacService.updateTblNetworkIpMac(tblNetworkIpMac);
//        }
        // 先删除
        tblNetworkIpMacService.deleteTblNetworkIpMacByAssetId(tblServer.getAssetId());
        // 后重新插入
        List<TblNetworkIpMac> ipMacArr = tblServer.getIpMacArr();
        for (TblNetworkIpMac ipMac : ipMacArr) {
            ipMac.setAssetId(tblServer.getAssetId());
            tblNetworkIpMacService.insertTblNetworkIpMac(ipMac);
        }

        int i = tblServerMapper.updateTblServer(tblServer);
        //在线检测
        if(StrUtil.isNotBlank(tblServer.getIp())){
            List<JSONObject> assetList = new ArrayList<>(1);
            JSONObject assetInfo = new JSONObject();
            assetInfo.put("ip",tblServer.getIp());
            assetInfo.put("assetId",tblServer.getAssetId());
            assetList.add(assetInfo);
            assetOnlineTask.asyncAssetOnlineCheck(assetList);
        }
        return i;
    }

    /**
     * 批量删除服务器/存储设备
     *
     * @param assetIds 需要删除的服务器/存储设备主键
     * @return 结果
     */
    @Override
    public int deleteTblServerByAssetIds(Long[] assetIds) {
        for (Long id : assetIds) {
            tblNetworkIpMacService.deleteTblNetworkIpMacByAssetId(id);
        }
        tblAssetOverviewService.deleteTblAssetOverviewByIds(assetIds);
        return tblServerMapper.deleteTblServerByAssetIds(assetIds);
    }

    /**
     * 删除服务器/存储设备信息
     *
     * @param assetId 服务器/存储设备主键
     * @return 结果
     */
    @Override
    public int deleteTblServerByAssetId(Long assetId) {
        tblNetworkIpMacService.deleteTblNetworkIpMacByAssetId(assetId);
        tblAssetOverviewService.deleteTblAssetOverviewById(assetId);
        return tblServerMapper.deleteTblServerByAssetId(assetId);
    }

    @Override
    public String importServer(List<TblServer> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw new ServiceException("导入服务器/存储设备信息不能为空！");
        }
        if (list.size() > 500) {
            throw new ServiceException("最多支持500条数据导入！");
        }
        //
        Long deptId = SecurityUtils.getDeptId();
        Long userId = SecurityUtils.getUserId();

        SysDept sysDept = sysDeptService.selectDeptById(deptId);
        NetworkDomain networkDomain = new NetworkDomain();
        networkDomain.setDeptId(sysDept.getParentId());
        Map<String, Long> map = networkDomainService.selectNetworkDomainList(networkDomain).stream().collect(Collectors.toMap(NetworkDomain::getDomainName, NetworkDomain::getDomainId));

        TblProduct product = new TblProduct();
        // 操作系统
        product.setProcType("system");
        Map<String, String> systemMap = tblProductService.selectTblProductList(product).stream().collect(Collectors.toMap(TblProduct::getProcName, TblProduct::getPrdid, (key1, key2) -> key2));
        // 数据库
        product.setProcType("database");
        Map<String, String> dateMap = tblProductService.selectTblProductList(product).stream().collect(Collectors.toMap(TblProduct::getProcName, TblProduct::getPrdid, (key1, key2) -> key2));
        // 中间件
        product.setProcType("middleware");
        Map<String, String> middleMap = tblProductService.selectTblProductList(product).stream().collect(Collectors.toMap(TblProduct::getProcName, TblProduct::getPrdid, (key1, key2) -> key2));
        //供应商， 设备厂商， 维保单位
        Map<String, Long> vendMap = tblVendorService.selectTblVendorList(new TblVendor()).stream().collect(Collectors.toMap(TblVendor::getVendorName, TblVendor::getId, (key1, key2) -> key2));
        // 所在位置
        Map<String, Long> locationMap = tblLocationService.selectTblLocationList(new TblLocation()).stream().collect(Collectors.toMap(TblLocation::getLocationFullName, TblLocation::getLocationId, (key1, key2) -> key2));
        TblAssetOverview asset = new TblAssetOverview();
        asset.setAssetClass(4L);
        Map<String, Long> assetMap = tblAssetOverviewService.selectTblAssetOverviewList(asset).stream().collect(Collectors.toMap(TblAssetOverview::getAssetName, TblAssetOverview::getAssetId, (key1, key2) -> key2));

        Map<Long, String> typeDesc = new HashMap<>();
        typeDesc.put(129L, "服务器");
        typeDesc.put(130L, "存储设备");
        typeDesc.put(132L, "虚拟机");
        typeDesc.put(133L, "云服务器");


        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        int countNum = 0;
        for (TblServer server : list) {
            countNum++;
            try {
                BeanValidators.validateWithException(validator, server);
                if (server.getAssetType() == null || server.getAssetType() == 0L) {
                    throw new ServiceException("资产类型不能为空！");
                }
                // 资产类型翻译
                server.setAssetTypeDesc(typeDesc.get(server.getAssetType()));
                // 操作系统, 数据库， 中间件
                buildServerParam(systemMap, "操作系统", server::getSystemName, server::setOptsystem,server.getSystemName());
                buildServerParam(dateMap, "数据库", server::getDbSystemName, server::setDbsystem,server.getDbSystemName());
                buildServerParam(middleMap, "中间件", server::getMdSystemName, server::setMdsystem,server.getMdSystemName());
                // //供应商， 设备厂商， 维保单位
                buildServerVendorOrNull(vendMap, "供应商", server::getVendorName, server::setVendor);
                buildServerVendorOrNull(vendMap, "设备厂商", server::getFacilityManufacturerName, server::setFacilityManufacturer);
                buildServerVendorOrNull(vendMap, "维保单位", server::getMaintainUnitName, server::setMaintainUnit);
                // 网络区域, 所属位置
                buildServerVendor(map, "网络区域", server::getDomainName, server::setDomainId);
                buildServerVendorThenAdd(locationMap, "所属位置", server::getLocationName, server::setLocationId);
                // 承载设备
                if ("Y".equals(server.getIsVirtual())) {
                    buildServerVendorOrNull(assetMap, "承载设备", server::getBaseAssetName, server::setBaseAsset);
                }
                // mac
                serverVailDate(server::getMac, "mac", "^([0-9a-fA-F]{2}(:|-)){5}[0-9a-fA-F]{2}$");
                // ip
                serverVailDate(server::getIp, "ip", "^(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)(\\.(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)){3}$");
                server.setUserId(userId);
                server.setDeptId(deptId);
                successNum++;
            } catch (Exception e) {
                failureNum++;
                String msg = "数据第" + countNum + "行数据导入失败。";
                failureMsg.append(msg + "错误信息：" + e.getMessage());
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "共" + failureNum + "条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            // 数据全部正确 才可填报
            for (TblServer server : list) {
                this.insertTblServer(server);
            }
            successMsg.insert(0, "数据导入成功！共" + successNum + "条， 数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 组装  验证字段 操作系统， 中间件， 数据库
     *
     * <AUTHOR>
     * @Description //
     * @Date 2023/9/14 10:29
     **/
    private void buildServerParam(Map<String, String> map, String name, Supplier<String> serverGet, Consumer<TblDeploy> setter ,String objectName) throws ServiceException {
        //
        if (StringUtils.isNotBlank(serverGet.get())) {
            TblDeploy tblDeploy = new TblDeploy();
            if (StringUtils.isNotBlank(map.get(serverGet.get()))) {
                tblDeploy.setProcName(serverGet.get());
                tblDeploy.setPrdid(map.get(serverGet.get()));
                setter.accept(tblDeploy);
            } else {
                /*throw new ServiceException(name + "：填报出错(系统中不存在/或输入错误),错误字段:"+objectName+"  ");*/
                tblDeploy.setProcName(null);
                tblDeploy.setPrdid(null);
                setter.accept(tblDeploy);
            }
        }
    }

    /**
     * 处理 供应商，设备厂商， 维保单位
     *
     * <AUTHOR>
     * @Description //
     * @Date 2023/9/14 14:14
     **/
    private void buildServerVendor(Map<String, Long> map, String name, Supplier<String> serverGet, Consumer<Long> setter) {
        Long id = map.get(serverGet.get());
        if (StringUtils.isNotBlank(serverGet.get())) {
            if (id != null && id != 0L) {
                setter.accept(id);
            } else {
                throw new ServiceException(name + "：填报出错(为空或不存在)");
            }
        }
    }

    private void buildServerVendorOrNull(Map<String, Long> map, String name, Supplier<String> serverGet, Consumer<Long> setter) {
        Long id = map.get(serverGet.get());
        if (StringUtils.isNotBlank(serverGet.get())) {
            if (id != null && id != 0L) {
                setter.accept(id);
            } else {
                /*throw new ServiceException(name + "：填报出错(为空或不存在)");*/
                setter.accept(null);
            }
        }
    }

    private void buildServerVendorThenAdd(Map<String, Long> map, String name, Supplier<String> serverGet, Consumer<String> setter) {
        Long id = map.get(serverGet.get());
        if (StringUtils.isNotBlank(serverGet.get())) {
            if (id != null && id != 0L) {
                setter.accept(StrUtil.toString(id));
            } else {
                throw new ServiceException(name + "：填报出错(为空或不存在)");
            }
        }
    }

    /**
     * 验证字段 格式
     *
     * @return
     * <AUTHOR>
     * @Description //
     * @Date 2023/9/14 16:27
     **/
    private void serverVailDate(Supplier<String> serverGet, String name, String regex) {
        if (StringUtils.isNotBlank(serverGet.get())) {
            Pattern pattern = Pattern.compile(regex);
            if (!pattern.matcher(serverGet.get()).matches()) {
                throw new ServiceException(name + " 格式有误。 ");
            }
        }
    }


    /**
     * 资产选择通用组件查询
     */
    @Override
    public List<TblServer> assetSelectByServer(HashMap<String, String> params) {
        List<TblServer> tblServers = tblServerMapper.assetSelectByServer(params);
        tblServers.forEach(server -> server.setAssetType(0L));
        return tblServers;
    }

    /**
     * 资产选择通用组件查询
     */
    @Override
    public List<TblServer> assetSelectByServer2(HashMap<String, String> params) {
        List<TblServer> tblServers = tblServerMapper.assetSelectByServer2(params);
        tblServers.forEach(server -> server.setAssetType(0L));
        return tblServers;
    }

    @Override
    public List<JSONObject> selectTblServerLocationIdIsNotNull() {
        return tblServerMapper.selectTblServerLocationIdIsNotNull();
    }

    @Override
    public int unbind(TblServer tblServer) {
        Assert.notNull(tblServer);
        Assert.notNull(tblServer.getAssetId());
        tblServer.setCwHostId(0);
        tblServer.setUpdateTime(DateUtil.date());
        return tblServerMapper.updateTblServer(tblServer);
    }

    @Override
    public List<JSONObject> unionAssetListByCondition(HashMap<String,String> params) {
        return tblServerMapper.unionAssetListByCondition(params);
    }

    @Override
    public EdrDetailsResult selectEdrByAssetId(Long assetId) throws IOException {
        ObjectMapper objectMapper = new ObjectMapper();
        CloseableHttpClient httpClient = null;
        HttpResponse response = null;
        // 根据assetId查询tbl_server获取serial
        TblServer tblServer = tblServerMapper.selectTblServerByAssetId(assetId);
        if (StringUtils.isEmpty(tblServer.getSerial())) {
            return new EdrDetailsResult();
        }
        edrDetailParam.parseParam(tblServer.getSerial());
        try {
            httpClient = (CloseableHttpClient) SkipHttpsUtil.wrapClient();
            response = httpClient.execute(edrDetailParam.getRequestBase(edrDetailParam.getDeviceConfigId()));
            if (response != null) {
                HttpEntity resEntity = response.getEntity();
                if (resEntity != null) {
                    String result = EntityUtils.toString(resEntity, "utf-8");
                    if (result.contains("hardwareDetails")) {
                        EdrDetailsResult taskResult = objectMapper.readValue(result, EdrDetailsResult.class);
                        return taskResult;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("host_detail_by_serial 接口调用错误: " + e.getMessage());
            throw e;
        } finally {
            try {
                if (httpClient != null) {
                    httpClient.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
                log.error("host_detail_by_serial 接口调用错误: " + e.getMessage());
                throw e;
            }
        }
        return new EdrDetailsResult();
    }

    @Override
    public List<TreeSelect> getDeptServerCount(QueryDeptServerCountDto queryCountDto) {
        queryCountDto.getSysDept().setDeptId(null);
        // 获取部门树列表
        List<TreeSelect> treeSelects = deptService.selectDeptTreeList(queryCountDto.getSysDept());
        // 获取部门ID列表
        List<Long> deptIdList = getDeptIdList(treeSelects);
        if (CollUtil.isNotEmpty(deptIdList)) {
            queryCountDto.setDeptIdList(deptIdList);
            List<DeptServerCount> deptServerCounts = tblServerMapper.getDeptServerCount(queryCountDto);
            // 创建部门ID与服务器计数的映射
            Map<Long, DeptServerCount> serverCountMap = deptServerCounts.stream()
                    .collect(Collectors.toMap(DeptServerCount::getDeptId, Function.identity()));
            // 计算祖先服务器计数
            Map<Long, DeptServerCount> updatedServerCountMap = calculateAncestorServerCounts(serverCountMap);
            // 应用并排序服务器计数
            applyAndSortServerCounts(treeSelects, updatedServerCountMap);
        }
        return treeSelects;
    }

    /**
     * 计算祖先服务器计数
     *
     * @param serverCountMap
     * @return
     */
    private Map<Long, DeptServerCount> calculateAncestorServerCounts(Map<Long, DeptServerCount> serverCountMap) {
        Map<Long, Integer> ancestorCounts = new HashMap<>();
        // Step 1: 计算每个祖先的服务器计数总和
        for (DeptServerCount dept : serverCountMap.values()) {
            String[] ancestors = dept.getAncestors().split(",");
            for (String ancestorId : ancestors) {
                if (NumberUtil.isLong(ancestorId)) {
                    ancestorCounts.merge(Long.parseLong(ancestorId), dept.getServerCount(), Integer::sum);
                }
            }
        }
        // Step 2: 使用为每个祖先计算的总和更新服务器计数
        return serverCountMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey,
                        entry -> new DeptServerCount(entry.getKey(), entry.getValue().getAncestors(),
                                ancestorCounts.getOrDefault(entry.getKey(), 0) + entry.getValue().getServerCount())));
    }

    /**
     * 应用并排序服务器计数
     */
    private void applyAndSortServerCounts(List<TreeSelect> treeSelects, Map<Long, DeptServerCount> serverCountMap) {
        for (TreeSelect treeSelect : treeSelects) {
            DeptServerCount serverCount = serverCountMap.get(treeSelect.getId());
            if (serverCount != null) {
                int count = serverCount.getServerCount() != null ? serverCount.getServerCount() : 0;
                treeSelect.setCount(count);
            }
            List<TreeSelect> children = treeSelect.getChildren();
            if (CollUtil.isNotEmpty(children)) {
                // 按服务器计数降序对子项进行排序
                children.sort(Comparator.comparing((TreeSelect child) -> {
                    DeptServerCount count = serverCountMap.getOrDefault(child.getId(), new DeptServerCount(child.getId(), "", 0));
                    return count.getServerCount();
                }).reversed().thenComparing(TreeSelect::getId));
                // 递归处理子项
                applyAndSortServerCounts(children, serverCountMap);
            }
        }
    }

    /**
     * 获取部门ID列表
     *
     * @param treeSelects
     * @return
     */
    private List<Long> getDeptIdList(List<TreeSelect> treeSelects) {
        List<Long> deptIds = new ArrayList<>();
        if (CollUtil.isNotEmpty(treeSelects)) {
            recursiveGetDeptIds(treeSelects, deptIds);
        }
        return deptIds;
    }

    /**
     * 递归获取部门ID列表
     *
     * @param nodes
     * @param deptIds
     */
    private void recursiveGetDeptIds(List<TreeSelect> nodes, List<Long> deptIds) {
        for (TreeSelect node : nodes) {
            deptIds.add(node.getId());
            if (CollUtil.isNotEmpty(node.getChildren())) {
                recursiveGetDeptIds(node.getChildren(), deptIds);
            }
        }
    }

    /**
     * 字典映射数据容器
     * 用于存储导入过程中需要的所有字典数据映射
     */
    private static class DictionaryMaps {
        private Map<String, Long> vendorMap = new HashMap<>();
        private Map<String, Long> deptMap = new HashMap<>();
        private Map<String, Long> applicationMap = new HashMap<>();
        private Map<String, String> systemProductMap = new HashMap<>();
        private Map<String, String> dbProductMap = new HashMap<>();
        private Map<String, Long> locationMap = new HashMap<>();
        private List<NetworkDomain> networkDomains = new ArrayList<>();

        // Getters and Setters
        public Map<String, Long> getVendorMap() { return vendorMap; }
        public void setVendorMap(Map<String, Long> vendorMap) { this.vendorMap = vendorMap; }

        public Map<String, Long> getDeptMap() { return deptMap; }
        public void setDeptMap(Map<String, Long> deptMap) { this.deptMap = deptMap; }

        public Map<String, Long> getApplicationMap() { return applicationMap; }
        public void setApplicationMap(Map<String, Long> applicationMap) { this.applicationMap = applicationMap; }

        public Map<String, String> getSystemProductMap() { return systemProductMap; }
        public void setSystemProductMap(Map<String, String> systemProductMap) { this.systemProductMap = systemProductMap; }

        public Map<String, String> getDbProductMap() { return dbProductMap; }
        public void setDbProductMap(Map<String, String> dbProductMap) { this.dbProductMap = dbProductMap; }

        public Map<String, Long> getLocationMap() { return locationMap; }
        public void setLocationMap(Map<String, Long> locationMap) { this.locationMap = locationMap; }

        public List<NetworkDomain> getNetworkDomains() { return networkDomains; }
        public void setNetworkDomains(List<NetworkDomain> networkDomains) { this.networkDomains = networkDomains; }
    }

    /**
     * 批量查询字典数据
     * 一次性查询所有需要的字典数据，避免在循环中重复查询数据库，提高性能
     *
     * @return DictionaryMaps 包含所有字典映射的容器对象
     */
    private DictionaryMaps prepareDictionaryMaps() {
        DictionaryMaps maps = new DictionaryMaps();

        try {
            // 1. 查询供应商映射：供应商名称 -> 供应商ID
            List<TblVendor> vendorList = tblVendorService.selectTblVendorList(new TblVendor());
            if (CollUtil.isNotEmpty(vendorList)) {
                maps.setVendorMap(vendorList.stream()
                    .filter(vendor -> StringUtils.isNotBlank(vendor.getVendorName()))
                    .collect(Collectors.toMap(
                        TblVendor::getVendorName,
                        TblVendor::getId, // 修正：使用getId()而不是getVendorId()
                        (key1, key2) -> key2 // 处理重复key，保留后者
                    )));
            }

            // 2. 查询部门映射：部门名称 -> 部门ID
            List<SysDept> deptList = sysDeptService.selectDeptList(new SysDept());
            if (CollUtil.isNotEmpty(deptList)) {
                maps.setDeptMap(deptList.stream()
                    .filter(dept -> StringUtils.isNotBlank(dept.getDeptName()))
                    .collect(Collectors.toMap(
                        SysDept::getDeptName,
                        SysDept::getDeptId,
                        (key1, key2) -> key2
                    )));
            }

            // 3. 查询业务系统映射：业务系统名称 -> 业务系统ID
            List<TblBusinessApplication> applicationList = tblBusinessApplicationService.selectTblBusinessApplicationList(new TblBusinessApplication());
            if (CollUtil.isNotEmpty(applicationList)) {
                maps.setApplicationMap(applicationList.stream()
                    .filter(app -> StringUtils.isNotBlank(app.getAssetName()))
                    .collect(Collectors.toMap(
                        TblBusinessApplication::getAssetName,
                        TblBusinessApplication::getAssetId,
                        (key1, key2) -> key2
                    )));
            }

            // 4. 查询产品映射：产品名称 -> 产品ID
            List<TblProduct> productList = tblProductService.selectTblProductList(new TblProduct());
            if (CollUtil.isNotEmpty(productList)) {
                // 操作系统产品映射（根据procType字段区分）
                maps.setSystemProductMap(productList.stream()
                    .filter(product -> StringUtils.isNotBlank(product.getProcName()) && "system".equals(product.getProcType()))
                    .collect(Collectors.toMap(
                        TblProduct::getProcName,
                        product -> String.valueOf(product.getPrdid()),
                        (key1, key2) -> key2
                    )));

                // 数据库产品映射（根据procType字段区分）
                maps.setDbProductMap(productList.stream()
                    .filter(product -> StringUtils.isNotBlank(product.getProcName()) && "database".equals(product.getProcType()))
                    .collect(Collectors.toMap(
                        TblProduct::getProcName,
                        product -> String.valueOf(product.getPrdid()),
                        (key1, key2) -> key2
                    )));
            }

            // 5. 查询位置映射：位置名称 -> 位置ID
            List<TblLocation> locationList = tblLocationService.selectTblLocationList(new TblLocation());
            if (CollUtil.isNotEmpty(locationList)) {
                maps.setLocationMap(locationList.stream()
                    .filter(location -> StringUtils.isNotBlank(location.getLocationFullName()))
                    .collect(Collectors.toMap(
                        TblLocation::getLocationFullName,
                        TblLocation::getLocationId,
                        (key1, key2) -> key2
                    )));
            }

            // 6. 查询网络区域列表（用于IP匹配）
            List<NetworkDomain> networkDomainList = networkDomainService.selectNetworkDomainList(new NetworkDomain());
            maps.setNetworkDomains(networkDomainList != null ? networkDomainList : new ArrayList<>());

        } catch (Exception e) {
            log.error("批量查询字典数据失败", e);
            throw new ServiceException("查询字典数据失败：" + e.getMessage());
        }

        return maps;
    }

    /**
     * 根据IP地址匹配网络区域
     * 复用现有的IpAddressMatcher逻辑，遍历网络区域列表进行CIDR匹配
     *
     * @param ip IP地址
     * @param domains 网络区域列表
     * @return 匹配的网络区域ID，未匹配到返回null
     */
    private Long getDomainIdByIp(String ip, List<NetworkDomain> domains) {
        // IP地址格式验证
        if (StringUtils.isBlank(ip)) {
            log.debug("IP地址为空，无法匹配网络区域");
            return null;
        }

        // 网络区域列表验证
        if (CollUtil.isEmpty(domains)) {
            log.debug("网络区域列表为空，无法匹配网络区域");
            return null;
        }

        try {
            // 遍历网络区域列表，使用IpAddressMatcher进行CIDR匹配
            for (NetworkDomain domain : domains) {
                if (domain != null && StringUtils.isNotBlank(domain.getIparea())) {
                    try {
                        // 创建IP地址匹配器，支持CIDR格式（如***********/24）
                        /*IpAddressMatcher ipAddressMatcher = new IpAddressMatcher(domain.getIparea());

                        // 执行匹配
                        if (ipAddressMatcher.matches(ip)) {
                            log.debug("IP地址 {} 匹配到网络区域：{} ({})", ip, domain.getDomainName(), domain.getIparea());
                            return domain.getDomainId();
                        }*/
                        if(IpUtils.isInRange(ip, domain.getIparea())){
                            log.debug("IP地址 {} 匹配到网络区域：{} ({})", ip, domain.getDomainName(), domain.getIparea());
                            return domain.getDomainId();
                        }
                    } catch (Exception e) {
                        // 单个网络区域匹配失败不影响其他区域的匹配
                        log.warn("网络区域 {} 的IP范围格式错误：{}，跳过该区域", domain.getDomainName(), domain.getIparea(), e);
                    }
                }
            }

            // 未匹配到任何网络区域
            log.debug("IP地址 {} 未匹配到任何网络区域", ip);
            return null;

        } catch (Exception e) {
            log.error("IP地址匹配网络区域时发生异常：IP={}", ip, e);
            return null;
        }
    }

    /**
     * 获取或创建供应商
     * 根据供应商名称查询现有供应商，如果不存在则自动创建新的供应商记录
     *
     * @param vendorName 供应商名称
     * @param vendorMap 供应商映射缓存
     * @return 供应商ID
     */
    private Long getOrCreateVendor(String vendorName, Map<String, Long> vendorMap) {
        // 参数验证
        if (StringUtils.isBlank(vendorName)) {
            log.warn("供应商名称为空，无法创建供应商");
            return null;
        }

        try {
            // 首先检查缓存中是否存在
            if (vendorMap.containsKey(vendorName)) {
                Long vendorId = vendorMap.get(vendorName);
                log.debug("从缓存中找到供应商：{} -> {}", vendorName, vendorId);
                return vendorId;
            }

            // 缓存中不存在，查询数据库（防止并发创建）
            TblVendor queryVendor = new TblVendor();
            queryVendor.setVendorName(vendorName);
            List<TblVendor> existingVendors = tblVendorService.selectTblVendorList(queryVendor);

            if (CollUtil.isNotEmpty(existingVendors)) {
                // 找到现有供应商，更新缓存
                TblVendor vendor = existingVendors.get(0);
                vendorMap.put(vendorName, vendor.getId());
                log.debug("从数据库中找到供应商：{} -> {}", vendorName, vendor.getId());
                return vendor.getId();
            }

            // 不存在，创建新供应商
            TblVendor newVendor = new TblVendor();
            newVendor.setVendorName(vendorName);
            newVendor.setVendorCode("AUTO_" + System.currentTimeMillis()); // 自动生成编码

            // 设置基础字段
            newVendor.setCreateTime(DateUtils.getNowDate());
            newVendor.setCreateBy(SecurityUtils.getUsername());
            newVendor.setUserId(SecurityUtils.getUserId());
            newVendor.setDeptId(SecurityUtils.getDeptId());

            // 保存到数据库
            int result = tblVendorService.insertTblVendor(newVendor);
            if (result > 0) {
                // 更新缓存
                vendorMap.put(vendorName, newVendor.getId());
                log.info("成功创建新供应商：{} -> {}", vendorName, newVendor.getId());
                return newVendor.getId();
            } else {
                log.error("创建供应商失败：{}", vendorName);
                return null;
            }

        } catch (Exception e) {
            log.error("获取或创建供应商时发生异常：vendorName={}", vendorName, e);
            return null;
        }
    }

    /**
     * 获取或创建产品
     * 根据产品名称和类型查询现有产品，如果不存在则自动创建新的产品记录
     *
     * @param productName 产品名称
     * @param type 产品类型："system"=操作系统，"database"=数据库
     * @param productMap 产品映射缓存
     * @return 产品ID字符串
     */
    private String getOrCreateProduct(String productName, String type, Map<String, String> productMap) {
        // 参数验证
        if (StringUtils.isBlank(productName) || StringUtils.isBlank(type)) {
            log.warn("产品名称或类型为空，无法创建产品：productName={}, type={}", productName, type);
            return null;
        }

        try {
            // 首先检查缓存中是否存在
            if (productMap.containsKey(productName)) {
                String productId = productMap.get(productName);
                log.debug("从缓存中找到产品：{} -> {}", productName, productId);
                return productId;
            }

            // 缓存中不存在，查询数据库（防止并发创建）
            TblProduct queryProduct = new TblProduct();
            queryProduct.setProcName(productName);
            queryProduct.setProcType(type);
            List<TblProduct> existingProducts = tblProductService.selectTblProductList(queryProduct);

            if (CollUtil.isNotEmpty(existingProducts)) {
                // 找到现有产品，更新缓存
                TblProduct product = existingProducts.get(0);
                productMap.put(productName, product.getPrdid());
                log.debug("从数据库中找到产品：{} -> {}", productName, product.getPrdid());
                return product.getPrdid();
            }

            // 不存在，创建新产品
            TblProduct newProduct = new TblProduct();
            newProduct.setPrdid(String.valueOf(snowflake.nextId())); // 使用雪花算法生成ID
            newProduct.setProcName(productName);
            newProduct.setProcType(type);
            newProduct.setManuName("自动导入"); // 默认厂商名称
            newProduct.setPubTime(DateUtils.getNowDate());
            newProduct.setUpdateTime(DateUtils.getNowDate());

            // 保存到数据库
            int result = tblProductService.insertTblProduct(newProduct);
            if (result > 0) {
                // 更新缓存
                productMap.put(productName, newProduct.getPrdid());
                log.info("成功创建新产品：{} ({}) -> {}", productName, type, newProduct.getPrdid());
                return newProduct.getPrdid();
            } else {
                log.error("创建产品失败：{} ({})", productName, type);
                return null;
            }

        } catch (Exception e) {
            log.error("获取或创建产品时发生异常：productName={}, type={}", productName, type, e);
            return null;
        }
    }

    /**
     * 将TblServerImportDTO转换为TblServer实体
     * 处理6种特殊字段的业务逻辑，包含详细的错误信息收集
     *
     * @param dto 导入DTO对象
     * @param maps 字典映射数据
     * @param rowIndex 行号（用于错误提示）
     * @param errorMessages 错误信息收集器
     * @param preGeneratedAssetCode 预生成的资产编码
     * @return TblServer实体对象，转换失败返回null
     */
    private TblServer buildServerFromDto(TblServerImportDTO dto, DictionaryMaps maps, int rowIndex, List<String> errorMessages, String preGeneratedAssetCode) {
        try {
            TblServer server = new TblServer();

            // 1. 基础字段映射
            server.setAssetName(dto.getAssetName());
            server.setFacilityType(dto.getFacilityType());
            server.setHostName(dto.getHostName());
            server.setRemark(dto.getRemark());

            // 2. isSparing字段转换："是"->"1"，"否"->"0"
            if (StringUtils.isNotBlank(dto.getIsSparing())) {
                server.setIsSparing(dto.getIsSparing().equals("是") ? "1" : "0");
            } else {
                server.setIsSparing("0"); // 默认值
            }

            // 3. 设备厂商处理：调用getOrCreateVendor获取facilityManufacturer
            if (StringUtils.isNotBlank(dto.getFacilityManufacturerName())) {
                Long facilityManufacturerId = getOrCreateVendor(dto.getFacilityManufacturerName(), maps.getVendorMap());
                if (facilityManufacturerId != null) {
                    server.setFacilityManufacturer(facilityManufacturerId);
                    server.setFacilityManufacturerName(dto.getFacilityManufacturerName());
                } else {
                    errorMessages.add("第" + (rowIndex + 3) + "行：设备厂商处理失败");
                }
            }

            // 4. 供应商处理：调用getOrCreateVendor获取vendor
            if (StringUtils.isNotBlank(dto.getVendorName())) {
                Long vendorId = getOrCreateVendor(dto.getVendorName(), maps.getVendorMap());
                if (vendorId != null) {
                    server.setVendor(vendorId);
                    server.setVendorName(dto.getVendorName());
                } else {
                    errorMessages.add("第" + (rowIndex + 3) + "行：供应商处理失败");
                }
            }

            // 5. 部门处理：从deptMap获取deptId，不存在则抛出异常
            if (StringUtils.isNotBlank(dto.getDeptName())) {
                Long deptId = maps.getDeptMap().get(dto.getDeptName());
                if (deptId != null) {
                    server.setDeptId(deptId);
                    server.setDeptName(dto.getDeptName());
                } else {
                    errorMessages.add("第" + (rowIndex + 3) + "行：部门 '" + dto.getDeptName() + "' 不存在，请先在系统中创建该部门");
                    return null; // 部门不存在必须阻止导入
                }
            }

            // 6. 操作系统处理：调用getOrCreateProduct创建TblDeploy对象
            if (StringUtils.isNotBlank(dto.getSystemName())) {
                String systemProductId = getOrCreateProduct(dto.getSystemName(), "system", maps.getSystemProductMap());
                if (systemProductId != null) {
                    TblDeploy optsystem = new TblDeploy();
                    optsystem.setPrdid(systemProductId);
                    optsystem.setProcName(dto.getSystemName());
                    optsystem.setSoftlx("1"); // 1=操作系统
                    server.setOptsystem(optsystem);
                    server.setSystemName(dto.getSystemName());
                }
            }

            // 7. 数据库处理：同上，设置softlx=2
            if (StringUtils.isNotBlank(dto.getDbSystemName())) {
                String dbProductId = getOrCreateProduct(dto.getDbSystemName(), "database", maps.getDbProductMap());
                if (dbProductId != null) {
                    TblDeploy dbsystem = new TblDeploy();
                    dbsystem.setPrdid(dbProductId);
                    dbsystem.setProcName(dto.getDbSystemName());
                    dbsystem.setSoftlx("2"); // 2=数据库
                    server.setDbsystem(dbsystem);
                    server.setDbSystemName(dto.getDbSystemName());
                }
            }

            // 8. 业务系统关联处理
            if (StringUtils.isNotBlank(dto.getApplicationName())) {
                Long applicationId = maps.getApplicationMap().get(dto.getApplicationName());
                if (applicationId != null) {
                    server.setApplicationId(applicationId);
                    server.setApplicationName(dto.getApplicationName());
                } else {
                    // 业务系统不存在时记录警告，但不阻止导入
                    log.warn("第{}行：业务系统 '{}' 不存在，将跳过关联", (rowIndex + 3), dto.getApplicationName());
                    server.setApplicationName(dto.getApplicationName()); // 仍然保存名称用于显示
                }
            }

            // 9. 位置处理：从locationMap获取locationId
            if (StringUtils.isNotBlank(dto.getLocationName())) {
                Long locationId = maps.getLocationMap().get(dto.getLocationName());
                if (locationId != null) {
                    server.setLocationId(String.valueOf(locationId));
                }
                server.setLocationName(dto.getLocationName());
            }

            // 10. IP地址匹配网络区域
            if (StringUtils.isNotBlank(dto.getIp())) {
                Long domainId = getDomainIdByIp(dto.getIp(), maps.getNetworkDomains());
                if (domainId != null) {
                    server.setDomainId(domainId);
                    // 查找网络区域名称
                    maps.getNetworkDomains().stream()
                        .filter(domain -> domain.getDomainId().equals(domainId))
                        .findFirst()
                        .ifPresent(domain -> server.setDomainName(domain.getDomainName()));
                }
                server.setIp(dto.getIp()); // 设置IP地址到server对象
            }

            // 11. MAC地址保存（将在后续保存IP-MAC关联表时处理）
            if (StringUtils.isNotBlank(dto.getMac())) {
                server.setMac(dto.getMac()); // 暂存MAC地址，后续保存到tbl_network_ip_mac表
            }

            // 12. 虚拟设备处理逻辑
            if (StringUtils.isNotBlank(dto.getIsVirtual()) && "Y".equals(dto.getIsVirtual())) {
                // 设置为虚拟设备
                server.setIsVirtual("Y");

                // 处理承载设备关联
                Long baseAssetId = handleVirtualDeviceAssociation(dto, maps, rowIndex, errorMessages);
                if (baseAssetId != null) {
                    server.setBaseAsset(baseAssetId);
                    log.info("第{}行：虚拟设备成功关联到承载设备ID={}", (rowIndex + 3), baseAssetId);
                } else {
                    log.warn("第{}行：虚拟设备未能关联到承载设备", (rowIndex + 3));
                }
            } else {
                // 非虚拟设备，设置默认值
                server.setIsVirtual("N");
            }

            // 13. 设置默认值：assetId、assetCode、assetClass、assetType、createTime等
            server.setAssetId(snowflake.nextId()); // 使用雪花算法生成资产ID

            // 设置资产编码：使用预生成的编码，确保唯一性和高性能
            if (StringUtils.isBlank(server.getAssetCode()) && StringUtils.isNotBlank(preGeneratedAssetCode)) {
                server.setAssetCode(preGeneratedAssetCode);
                log.debug("第{}行设置资产编码：{}", (rowIndex + 3), preGeneratedAssetCode);
            }

            server.setAssetClass(4L); // 4L = 服务器类

            // 14. 根据用户选择的资产类型动态设置assetType和assetTypeDesc
            setAssetTypeFromDto(dto, server, rowIndex);
            server.setDegreeImportance("3"); // 默认重要程度：一般
            server.setState("0"); // 默认在线状态

            // 设置审计字段
            server.setCreateTime(DateUtils.getNowDate());
            server.setCreateBy(SecurityUtils.getUsername());
            server.setUserId(SecurityUtils.getUserId());
            server.setUpdateTime(DateUtils.getNowDate());
            if (server.getDeptId() == null) {
                server.setDeptId(SecurityUtils.getDeptId()); // 如果没有指定部门，使用当前用户部门
            }

            return server;

        } catch (Exception e) {
            log.error("DTO转换为实体时发生异常：rowIndex={}, dto={}", rowIndex, dto, e);
            errorMessages.add("第" + (rowIndex + 3) + "行：数据转换失败 - " + e.getMessage());
            return null;
        }
    }

    /**
     * 处理虚拟设备的承载设备关联逻辑
     * 当isVirtual="是"时，根据所属部门查找网络区域，在区域内通过承载设备IP查找匹配的服务器
     * 匹配规则：1台建立关联，0台或多台不建立关联
     *
     * @param dto 导入DTO对象
     * @param maps 字典映射数据
     * @param rowIndex 行索引
     * @param errorMessages 错误信息收集器
     * @return 承载设备的资产ID，未匹配到返回null
     */
    private Long handleVirtualDeviceAssociation(TblServerImportDTO dto, DictionaryMaps maps, int rowIndex, List<String> errorMessages) {
        try {
            // 1. 参数验证
            if (StringUtils.isBlank(dto.getBaseAssetIp())) {
                log.warn("第{}行：虚拟设备未填写承载设备IP，跳过关联", (rowIndex + 3));
                return null;
            }

            if (StringUtils.isBlank(dto.getDeptName())) {
                log.warn("第{}行：虚拟设备未填写所属部门，无法确定网络区域", (rowIndex + 3));
                return null;
            }

            // 2. 根据所属部门查找对应的网络区域
            Long deptId = maps.getDeptMap().get(dto.getDeptName());
            if (deptId == null) {
                log.warn("第{}行：部门'{}'不存在，无法查找网络区域", (rowIndex + 3), dto.getDeptName());
                return null;
            }

            List<NetworkDomain> deptNetworkDomains = getNetworkDomainsByDept(deptId, maps.getNetworkDomains());
            if (CollUtil.isEmpty(deptNetworkDomains)) {
                log.warn("第{}行：部门'{}'没有对应的网络区域", (rowIndex + 3), dto.getDeptName());
                return null;
            }

            // 3. 在这些网络区域内查找承载设备IP匹配的服务器
            List<TblServer> matchedServers = findServersByIpInDomains(dto.getBaseAssetIp(), deptNetworkDomains);

            // 4. 根据匹配结果决定是否建立关联
            if (matchedServers.size() == 1) {
                // 匹配到1台服务器：建立关联关系
                TblServer baseServer = matchedServers.get(0);
                log.info("第{}行：虚拟设备成功关联到承载设备，承载设备IP={}，承载设备名称={}，承载设备ID={}",
                    (rowIndex + 3), dto.getBaseAssetIp(), baseServer.getAssetName(), baseServer.getAssetId());
                return baseServer.getAssetId();
            } else if (matchedServers.size() == 0) {
                // 匹配到0台服务器：不建立关联关系
                log.warn("第{}行：在部门'{}'的网络区域内未找到承载设备IP'{}'对应的服务器",
                    (rowIndex + 3), dto.getDeptName(), dto.getBaseAssetIp());
                return null;
            } else {
                // 匹配到多台服务器：不建立关联关系
                log.warn("第{}行：在部门'{}'的网络区域内找到{}台承载设备IP'{}'对应的服务器，无法确定唯一关联",
                    (rowIndex + 3), dto.getDeptName(), matchedServers.size(), dto.getBaseAssetIp());
                return null;
            }

        } catch (Exception e) {
            log.error("第{}行：虚拟设备关联处理失败", (rowIndex + 3), e);
            errorMessages.add("第" + (rowIndex + 3) + "行：虚拟设备关联处理失败 - " + e.getMessage());
            return null;
        }
    }

    /**
     * 根据部门ID查找对应的网络区域列表
     *
     * @param deptId 部门ID
     * @param allDomains 所有网络区域列表
     * @return 该部门对应的网络区域列表
     */
    private List<NetworkDomain> getNetworkDomainsByDept(Long deptId, List<NetworkDomain> allDomains) {
        if (deptId == null || CollUtil.isEmpty(allDomains)) {
            return new ArrayList<>();
        }

        try {
            // 根据部门ID筛选网络区域
            // 这里假设网络区域的dept_id字段与部门ID对应
            return allDomains.stream()
                .filter(domain -> domain != null && Objects.equals(domain.getDeptId(), deptId))
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("根据部门ID查找网络区域失败：deptId={}", deptId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 在指定网络区域内查找IP匹配的服务器
     *
     * @param ip 要查找的IP地址
     * @param domains 网络区域列表
     * @return 匹配的服务器列表
     */
    private List<TblServer> findServersByIpInDomains(String ip, List<NetworkDomain> domains) {
        if (StringUtils.isBlank(ip) || CollUtil.isEmpty(domains)) {
            return new ArrayList<>();
        }

        try {
            // 1. 首先确认IP是否在这些网络区域内
            boolean ipInDomains = false;
            for (NetworkDomain domain : domains) {
                if (domain != null && StringUtils.isNotBlank(domain.getIparea())) {
                    try {
                        if (IpUtils.isInRange(ip, domain.getIparea())) {
                            ipInDomains = true;
                            break;
                        }
                    } catch (Exception e) {
                        log.warn("网络区域IP范围格式错误：{}，跳过该区域", domain.getIparea(), e);
                    }
                }
            }

            if (!ipInDomains) {
                log.debug("IP地址{}不在指定的网络区域内", ip);
                return new ArrayList<>();
            }

            // 2. 查找该IP对应的服务器
            TblServer queryServer = new TblServer();
            queryServer.setIp(ip);
            List<TblServer> servers = tblServerMapper.selectTblServerList(queryServer);

            log.debug("在网络区域内找到{}台IP为{}的服务器", servers.size(), ip);
            return servers != null ? servers : new ArrayList<>();

        } catch (Exception e) {
            log.error("在网络区域内查找服务器失败：ip={}", ip, e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据DTO中的资产类型设置服务器的assetType和assetTypeDesc
     * 注意：由于Excel的readConverterExp配置，用户选择显示名称时返回的是key值
     * 支持的资产类型映射：
     * - "129" (用户选择"服务器") → assetType=129L, assetTypeDesc="服务器"
     * - "130" (用户选择"存储设备") → assetType=130L, assetTypeDesc="存储设备"
     * - "132" (用户选择"虚拟机") → assetType=132L, assetTypeDesc="虚拟机"
     * - "133" (用户选择"云服务器") → assetType=133L, assetTypeDesc="云服务器"
     *
     * @param dto 导入DTO对象
     * @param server 服务器实体对象
     * @param rowIndex 行索引，用于日志记录
     */
    private void setAssetTypeFromDto(TblServerImportDTO dto, TblServer server, int rowIndex) {
        try {
            String assetTypeName = dto.getAssetType();

            // 如果资产类型为空，设置默认值
            if (StringUtils.isBlank(assetTypeName)) {
                server.setAssetType(129L);
                server.setAssetTypeDesc("服务器");
                log.debug("第{}行：资产类型为空，设置为默认值：服务器(129)", (rowIndex + 3));
                return;
            }

            // 根据资产类型key值映射到对应的ID和描述
            // Excel readConverterExp配置：用户选择"服务器"时返回"129"
            switch (assetTypeName.trim()) {
                case "129":
                    server.setAssetType(129L);
                    server.setAssetTypeDesc("服务器");
                    log.debug("第{}行：设置资产类型为服务器(129)，接收到key值：{}", (rowIndex + 3), assetTypeName);
                    break;
                case "130":
                    server.setAssetType(130L);
                    server.setAssetTypeDesc("存储设备");
                    log.debug("第{}行：设置资产类型为存储设备(130)，接收到key值：{}", (rowIndex + 3), assetTypeName);
                    break;
                case "132":
                    server.setAssetType(132L);
                    server.setAssetTypeDesc("虚拟机");
                    log.debug("第{}行：设置资产类型为虚拟机(132)，接收到key值：{}", (rowIndex + 3), assetTypeName);
                    break;
                case "133":
                    server.setAssetType(133L);
                    server.setAssetTypeDesc("云服务器");
                    log.debug("第{}行：设置资产类型为云服务器(133)，接收到key值：{}", (rowIndex + 3), assetTypeName);
                    break;
                default:
                    // 不匹配的资产类型key值，设置为默认值并记录警告
                    server.setAssetType(129L);
                    server.setAssetTypeDesc("服务器");
                    log.warn("第{}行：不支持的资产类型key值'{}'，设置为默认值：服务器(129)", (rowIndex + 3), assetTypeName);
                    break;
            }

        } catch (Exception e) {
            // 异常情况下设置默认值
            server.setAssetType(129L);
            server.setAssetTypeDesc("服务器");
            log.error("第{}行：设置资产类型时发生异常，设置为默认值：服务器(129)", (rowIndex + 3), e);
        }
    }

    /**
     * 保存业务系统关联关系
     * 根据applicationName查询tbl_business_application表，匹配到则在tbl_application_server中创建关联记录
     *
     * @param servers 服务器列表
     * @param maps 字典映射数据
     * @param errorMessages 错误信息收集器
     */
    private void saveApplicationServerRelations(List<TblServer> servers, DictionaryMaps maps, List<String> errorMessages) {
        if (CollUtil.isEmpty(servers)) {
            return;
        }

        try {
            List<TblApplicationServer> applicationServers = new ArrayList<>();

            for (TblServer server : servers) {
                // 只处理有业务系统关联的服务器
                if (server.getApplicationId() != null && server.getAssetId() != null) {
                    TblApplicationServer appServer = new TblApplicationServer();
                    appServer.setId(snowflake.nextId()); // 使用雪花算法生成ID
                    appServer.setAssetId(server.getApplicationId()); // 业务系统ID
                    appServer.setServerId(server.getAssetId()); // 服务器资产ID
                    // 0服务器环境,1数据环境
                    String type= "";
                    if(Objects.equals(server.getAssetType(), 129L)){
                        type= "0";
                    }
                    appServer.setType(type); // 关联类型

                    applicationServers.add(appServer);
                    log.debug("创建业务系统关联：业务系统ID={}, 服务器ID={}", server.getApplicationId(), server.getAssetId());
                }
            }

            // 批量保存业务系统关联
            if (CollUtil.isNotEmpty(applicationServers)) {
                int successCount = 0;
                for (TblApplicationServer appServer : applicationServers) {
                    try {
                        tblApplicationServerService.insertTblApplicationServer(appServer);
                        successCount++;
                    } catch (Exception e) {
                        log.warn("保存业务系统关联失败：{}", appServer, e);
                        errorMessages.add("业务系统关联保存失败：serverId=" + appServer.getServerId() + ", assetId=" + appServer.getAssetId() + ", " + e.getMessage());
                    }
                }
                int total = applicationServers.size();
                int failureCount = total - successCount;
                log.info("业务系统关联保存完成：成功{}条，失败{}条，总计{}条", successCount, failureCount, total);
            }

        } catch (Exception e) {
            log.error("批量保存业务系统关联时发生异常", e);
            errorMessages.add("业务系统关联保存异常：" + e.getMessage());
        }
    }

    /**
     * 保存IP-MAC关联数据
     * 创建TblNetworkIpMac对象，设置assetId、ipv4、mac、domainId，确保数据完整性
     *
     * @param servers 服务器列表
     * @param errorMessages 错误信息收集器
     */
    private void saveNetworkIpMacRelations(List<TblServer> servers, List<String> errorMessages) {
        if (CollUtil.isEmpty(servers)) {
            return;
        }

        try {
            List<TblNetworkIpMac> ipMacList = new ArrayList<>();

            for (TblServer server : servers) {
                // 只处理有IP地址的服务器
                if (StringUtils.isNotBlank(server.getIp()) && server.getAssetId() != null) {
                    TblNetworkIpMac ipMac = new TblNetworkIpMac();

                    // 设置基本信息
                    ipMac.setAssetId(server.getAssetId());
                    ipMac.setIpv4(server.getIp());
                    ipMac.setMainIp("1"); // 标记为主IP

                    // 设置MAC地址（如果有）
                    if (StringUtils.isNotBlank(server.getMac())) {
                        ipMac.setMac(server.getMac());
                    }

                    // 设置网络区域ID（如果匹配到）
                    if (server.getDomainId() != null) {
                        ipMac.setDomainId(server.getDomainId());
                    }

                    // 设置审计字段
                    ipMac.setCreateTime(DateUtils.getNowDate());
                    ipMac.setCreateBy(SecurityUtils.getUsername());
                    ipMac.setUserId(SecurityUtils.getUserId());
                    ipMac.setDeptId(server.getDeptId());

                    ipMacList.add(ipMac);
                    log.debug("创建IP-MAC关联：资产ID={}, IP={}, MAC={}, 网络区域ID={}",
                        server.getAssetId(), server.getIp(), server.getMac(), server.getDomainId());
                }
            }

            // 批量保存IP-MAC关联
            if (CollUtil.isNotEmpty(ipMacList)) {
                int successCount = 0;
                for (TblNetworkIpMac ipMac : ipMacList) {
                    try {
                        tblNetworkIpMacService.insertTblNetworkIpMac(ipMac);
                        successCount++;
                    } catch (Exception e) {
                        log.warn("保存IP-MAC关联失败：{}", ipMac, e);
                        if (Objects.equals(e.getMessage(),"请先选择网络区域！")) {
                            errorMessages.add("IP=" + ipMac.getIpv4()+" 未匹配到任何网络区域，" + "请先创建网络区域");
                        }else{
                            errorMessages.add(e.getMessage());
                        }
                    }
                }
                int total = ipMacList.size();
                int failureCount = total - successCount;
                log.info("IP-MAC关联保存完成：成功{}条，失败{}条，总计{}条", successCount, failureCount, total);
            }

        } catch (Exception e) {
            log.error("批量保存IP-MAC关联时发生异常", e);
            errorMessages.add("IP-MAC关联保存异常：" + e.getMessage());
        }
    }

    /**
     * 导入服务器数据（基于简化模板）
     * 整合所有子功能，实现完整的导入流程，包含数据验证、转换、保存和关联表处理
     *
     * @param dtoList TblServerImportDTO列表
     * @return 导入结果信息
     * @throws Exception 导入异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importServerFromTemplate(List<TblServerImportDTO> dtoList) throws Exception {
        log.info("开始导入服务器数据，数据量：{}", dtoList != null ? dtoList.size() : 0);

        // 1. 数据验证：空值检查、数量限制
        if (dtoList == null || dtoList.isEmpty()) {
            throw new ServiceException("导入服务器数据不能为空！");
        }
        if (dtoList.size() > 500) {
            throw new ServiceException("最多支持500条数据导入！");
        }

        // 2. 初始化统计变量和错误收集器
        int successNum = 0;
        int failureNum = 0;
        List<String> errorMessages = new ArrayList<>();
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        try {
            // 3. 调用prepareDictionaryMaps获取字典数据
            log.info("开始批量查询字典数据...");
            DictionaryMaps maps = prepareDictionaryMaps();
            log.info("字典数据查询完成");

            // 4. 批量生成资产编码（高效方案：预生成避免循环中的数据库查询）
            log.info("开始批量生成资产编码，数量：{}", dtoList.size());
            List<String> preGeneratedAssetCodes = batchGenerateAssetCodes(dtoList.size());
            log.info("资产编码批量生成完成");

            // 5. 数据转换和验证
            List<TblServer> validServers = new ArrayList<>();

            for (int i = 0; i < dtoList.size(); i++) {
                TblServerImportDTO dto = dtoList.get(i);
                int rowIndex = i;

                try {
                    // 基础数据验证
                    if (StringUtils.isBlank(dto.getAssetName())) {
                        errorMessages.add("第" + (rowIndex + 3) + "行：资产名称不能为空");
                        failureNum++;
                        continue;
                    }
                    if (StringUtils.isBlank(dto.getIp())) {
                        errorMessages.add("第" + (rowIndex + 3) + "行：主IP不能为空");
                        failureNum++;
                        continue;
                    }
                    if (StringUtils.isBlank(dto.getDeptName())) {
                        errorMessages.add("第" + (rowIndex + 3) + "行：管理部门不能为空");
                        failureNum++;
                        continue;
                    }

                    // IP格式验证
                    if (!dto.getIp().matches("^(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)(\\.(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)){3}$")) {
                        errorMessages.add("第" + (rowIndex + 3) + "行：主IP格式不正确");
                        failureNum++;
                        continue;
                    }

                    // MAC地址格式验证（如果有）
                    if (StringUtils.isNotBlank(dto.getMac()) &&
                        !dto.getMac().matches("^([0-9a-fA-F]{2}(:|-)){5}[0-9a-fA-F]{2}$")) {
                        errorMessages.add("第" + (rowIndex + 3) + "行：MAC地址格式不正确");
                        failureNum++;
                        continue;
                    }

                    // 调用buildServerFromDto进行数据转换，传入预生成的资产编码
                    String preGeneratedAssetCode = preGeneratedAssetCodes.get(i);
                    TblServer server = buildServerFromDto(dto, maps, rowIndex, errorMessages, preGeneratedAssetCode);
                    if (server != null) {
                        validServers.add(server);
                        successNum++;
                        log.debug("第{}行数据转换成功：{}", (rowIndex + 3), server.getAssetName());
                    } else {
                        failureNum++;
                        log.warn("第{}行数据转换失败", (rowIndex + 3));
                    }

                } catch (Exception e) {
                    failureNum++;
                    String msg = "第" + (rowIndex + 3) + "行数据处理失败：" + e.getMessage();
                    errorMessages.add(msg);
                    log.error("数据转换异常：rowIndex={}, dto={}", rowIndex, dto, e);
                }
            }

            // 5. 检查是否有错误，如果有则抛出异常
            if (failureNum > 0) {
                failureMsg.append("共").append(failureNum).append("条数据格式不正确，错误如下：\n");
                for (String error : errorMessages) {
                    failureMsg.append(error).append("\n");
                }
                throw new ServiceException(failureMsg.toString());
            }

            // 6. 批量保存：主表数据
            if (CollUtil.isNotEmpty(validServers)) {
                log.info("开始批量保存服务器主表数据，数量：{}", validServers.size());
                for (TblServer server : validServers) {
                    try {
                        // 保存到资产总表
                        tblAssetOverviewService.insertTblAssetOverview(server);
                        // 保存到服务器表
                        tblServerMapper.insertTblServer(server);

                        // 保存操作系统部署信息
                        if (server.getOptsystem() != null) {
                            server.getOptsystem().setAssetId(server.getAssetId());
                            server.getOptsystem().setDeployId(snowflake.nextId());
                            deployService.insertTblDeploy(server.getOptsystem());
                        }

                        // 保存数据库部署信息
                        if (server.getDbsystem() != null) {
                            server.getDbsystem().setAssetId(server.getAssetId());
                            server.getDbsystem().setDeployId(snowflake.nextId());
                            deployService.insertTblDeploy(server.getDbsystem());
                        }

                    } catch (Exception e) {
                        log.error("保存服务器数据失败：{}", server.getAssetName(), e);
                        throw new ServiceException("保存服务器数据失败：" + e.getMessage());
                    }
                }
                log.info("服务器主表数据保存完成");

                // 7. 批量保存：关联表数据
                log.info("开始保存关联表数据...");

                // 保存业务系统关联
                saveApplicationServerRelations(validServers, maps, errorMessages);

                // 保存IP-MAC关联
                saveNetworkIpMacRelations(validServers, errorMessages);

                // 如存在任何关联保存错误，终止导入并回滚事务
                if (CollUtil.isNotEmpty(errorMessages)) {
                    failureMsg.append("关联数据保存失败，错误如下：\n");
                    for (String error : errorMessages) {
                        failureMsg.append(error).append("\n");
                    }
                    throw new ServiceException(failureMsg.toString());
                }

                log.info("关联表数据保存完成");
            }

            // 8. 成功处理：返回成功信息和统计数据
            successMsg.append("数据导入成功！共").append(successNum).append("条，数据如下：\n");
            for (TblServer server : validServers) {
                successMsg.append("资产名称：").append(server.getAssetName())
                         .append("，IP地址：").append(server.getIp())
                         .append("，管理部门：").append(server.getDeptName()).append("\n");
            }

            log.info("服务器数据导入完成，成功：{}条，失败：{}条", successNum, failureNum);
            return successMsg.toString();

        } catch (Exception e) {
            log.error("导入服务器数据时发生异常", e);
            // 事务会自动回滚
            // 注意：不要在此处重复添加“导入失败：”前缀，交由控制器/全局异常处理器统一包装
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 批量生成资产编码
     * 高效方案：预生成所有编码，一次性数据库查询检查冲突，内存分配
     *
     * @param count 需要生成的编码数量
     * @return 生成的资产编码列表
     */
    private List<String> batchGenerateAssetCodes(int count) {
        List<String> codes = new ArrayList<>();
        if (count <= 0) {
            return codes;
        }

        try {
            // 1. 生成基础时间戳：ZJ + yyyyMMddHHmmss
            String baseTimestamp = getCurrentTimestamp();
            String baseCode = "ZJ" + baseTimestamp;

            // 2. 生成候选编码集合：ZJ + yyyyMMddHHmmss + 001, 002, 003...
            Set<String> candidateCodes = new HashSet<>();
            for (int i = 1; i <= count + 100; i++) { // 多生成一些以防冲突
                candidateCodes.add(baseCode + String.format("%03d", i));
            }

            // 3. 一次性批量查询数据库，检查哪些编码已存在
            List<String> existingCodesList = tblAssetOverviewMapper.checkBatchAssetCodesExist(candidateCodes);
            Set<String> existingCodes = new HashSet<>(existingCodesList);

            // 4. 分配可用编码
            int index = 1;
            for (int i = 0; i < count; i++) {
                String code = baseCode + String.format("%03d", index);
                while (existingCodes.contains(code)) {
                    index++;
                    code = baseCode + String.format("%03d", index);
                    // 防止无限循环
                    if (index > 9999) {
                        throw new ServiceException("资产编码生成失败：无法找到足够的可用编码");
                    }
                }
                codes.add(code);
                existingCodes.add(code); // 添加到已存在集合，避免重复分配
                index++;
            }

            log.info("批量生成资产编码成功，数量：{}，基础编码：{}", count, baseCode);
            return codes;

        } catch (Exception e) {
            log.error("批量生成资产编码失败", e);
            throw new ServiceException("资产编码生成失败：" + e.getMessage());
        }
    }

    /**
     * 获取当前时间戳（yyyyMMddHHmmss格式）
     * 与前端 getFormattedDate() 方法保持一致
     *
     * @return 格式化的时间戳字符串
     */
    private String getCurrentTimestamp() {
        Date now = new Date();
        return DateUtils.parseDateToStr("yyyyMMddHHmmss", now);
    }

}
