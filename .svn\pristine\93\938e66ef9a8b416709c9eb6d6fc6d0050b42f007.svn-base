package com.ruoyi.ffsafe.api.domain;

import cn.hutool.extra.spring.SpringUtil;
import com.ruoyi.ffsafe.api.service.ITblDeviceConfigService;
import com.ruoyi.ffsafe.scantaskapi.domain.ParamBase;
import com.ruoyi.ffsafe.scantaskapi.domain.RequestBase;
import com.ruoyi.monitor2.changting.client.FfsafeClientService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;

/**
 * 主机入侵攻击分页接口参数类
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@Slf4j
public class HostIntrusionAttackParam extends ParamBase implements RequestBase {

    /** 访问令牌 */
    private String accessToken;
    
    /** 源IP */
    private String sip;
    
    /** 目标IP */
    private String dip;
    
    /** 告警开始时间 */
    private String startDate;
    
    /** 告警结束时间 */
    private String endDate;
    
    /** 页数，默认值为1 */
    private int page = 1;
    
    /** 每页显示条数，默认值为20 */
    private int pSize = 20;
    
    /** 设备配置ID */
    private Long deviceConfigId;

    @Override
    public HttpRequestBase getRequestBase(Long deviceId) {
        TblDeviceConfig deviceConfig = FfsafeClientService.deviceConfigThreadLocal.get();
        ITblDeviceConfigService deviceConfigService = SpringUtil.getBean(ITblDeviceConfigService.class);
        FfsafeApiConfig ffsafeApiConfig = deviceConfigService.getFfsafeApiConfig(deviceConfig);
        String ffurl = ffsafeApiConfig.getUrl();
        String fftoken = ffsafeApiConfig.getToken();
        if (ffurl == null && !updateFfsafeApiConfig(deviceId)) {
            return null;
        }

        StringBuilder url = new StringBuilder(ffurl + "/v2/host-edr-intrusion");
        url.append("?access_token=").append(fftoken);

        if (sip != null && !sip.isEmpty()) {
            url.append("&sip=").append(sip);
        }

        if (dip != null && !dip.isEmpty()) {
            url.append("&dip=").append(dip);
        }

        if (startDate != null && !startDate.isEmpty()) {
            url.append("&start_date=").append(startDate.replaceAll(" ", "%20").replaceAll(":", "%3A"));
        }

        if (endDate != null && !endDate.isEmpty()) {
            url.append("&end_date=").append(endDate.replaceAll(" ", "%20").replaceAll(":", "%3A"));
        }

        url.append("&page=").append(page);
        url.append("&p_size=").append(pSize);

        log.info("非凡API主机入侵攻击分页请求参数: {}", url.toString());

        return new HttpPost(url.toString());
    }
}
