package com.ruoyi.ffsafe.api.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 主机入侵攻击详情VO
 * 用于返回攻击事件的完整信息，包括主表数据和详情表数据
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@Schema(name = "FfsafeHostIntrusionAttackDetailVO", description = "主机入侵攻击详情信息")
public class FfsafeHostIntrusionAttackDetailVO implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @Schema(description = "主键ID")
    private Long id;

    /** 非凡返回的ID */
    @Schema(description = "非凡返回的ID")
    private Integer ffId;

    /** 攻击源IP */
    @Schema(description = "攻击源IP")
    private String sip;

    /** 目标IP */
    @Schema(description = "目标IP")
    private String dip;

    /** 目标IP主机名 */
    @Schema(description = "目标IP主机名")
    private String dipName;

    /** 告警名称 */
    @Schema(description = "告警名称")
    private String alertName;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "开始时间")
    private Date startTime;

    /** 最近告警时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "最近告警时间")
    private Date updateTime;

    /** 处置状态: 0=未处置,1=已处置,2=忽略 */
    @Schema(description = "处置状态: 0=未处置,1=已处置,2=忽略")
    private Integer handleState;

    /** 处置描述 */
    @Schema(description = "处置描述")
    private String handleDesc;

    /** 处置人 */
    @Schema(description = "处置人")
    private String disposer;

    /** 设备配置ID */
    @Schema(description = "设备配置ID")
    private Long deviceConfigId;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;

    /** 创建者 */
    @Schema(description = "创建者")
    private String createBy;

    /** 更新者 */
    @Schema(description = "更新者")
    private String updateBy;

    /** 详情类型: brute_force=暴力破解,web_attack=Web攻击,vuln_scan=漏洞扫描 */
    @Schema(description = "详情类型")
    private String detailType;

    /** 详情数据JSON格式 */
    @Schema(description = "详情数据JSON格式")
    private String detailData;

    /**
     * 获取处置状态文本
     */
    public String getHandleStateText() {
        if (handleState == null) {
            return "未知";
        }
        switch (handleState) {
            case 0:
                return "未处置";
            case 1:
                return "已处置";
            case 2:
                return "忽略";
            default:
                return "未知";
        }
    }

    /**
     * 获取详情类型文本
     */
    public String getDetailTypeText() {
        if (detailType == null) {
            return "未知";
        }
        switch (detailType) {
            case "brute_force":
                return "暴力破解";
            case "web_attack":
                return "Web攻击";
            case "vuln_scan":
                return "漏洞扫描";
            default:
                return "未知";
        }
    }
}
