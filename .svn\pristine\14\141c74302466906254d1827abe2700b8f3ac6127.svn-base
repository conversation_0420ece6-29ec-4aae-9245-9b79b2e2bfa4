package com.ruoyi.ffsafe.api.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * 主机入侵攻击详情接口响应结果类
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@Slf4j
@JsonIgnoreProperties(ignoreUnknown = true)
public class HostIntrusionAttackDetailResult {
    
    /** 返回消息 */
    @JsonProperty("msg")
    private String msg;
    
    /** 详情数据 */
    @JsonProperty("data")
    private Object data;
    
    /**
     * 转换为详情实体对象
     * 
     * @param attackId 关联的攻击事件ID
     * @return 详情实体对象
     */
    public FfsafeHostIntrusionAttackDetail toDetailEntity(Long attackId) {
        if (data == null) {
            return null;
        }
        
        FfsafeHostIntrusionAttackDetail detail = new FfsafeHostIntrusionAttackDetail();
        detail.setAttackId(attackId);
        
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonData = objectMapper.writeValueAsString(data);
            detail.setDetailData(jsonData);
            
            // 根据数据内容判断详情类型
            String detailType = determineDetailType(data);
            detail.setDetailType(detailType);
            
        } catch (JsonProcessingException e) {
            log.error("转换详情数据为JSON失败", e);
            detail.setDetailData("{}");
            detail.setDetailType("other");
        }
        
        return detail;
    }
    
    /**
     * 根据数据内容判断详情类型
     * 
     * @param data 数据对象
     * @return 详情类型
     */
    private String determineDetailType(Object data) {
        if (data instanceof Map) {
            Map<String, Object> dataMap = (Map<String, Object>) data;
            
            // 暴力破解事件数据结构判断
            if (dataMap.containsKey("ip") && dataMap.containsKey("type") && 
                dataMap.containsKey("login_account") && dataMap.containsKey("try_counts")) {
                return "brute_force";
            }
            
            // Web攻击详情数据结构判断
            if (dataMap.containsKey("alert_name") && dataMap.containsKey("sip") && 
                dataMap.containsKey("dip") && dataMap.containsKey("log_alert_info")) {
                return "web_attack";
            }
            
            // 主机漏洞扫描详情数据结构判断
            if (dataMap.containsKey("alert_info")) {
                return "vuln_scan";
            }
        }
        
        // 默认为其他类型
        return "other";
    }
}
