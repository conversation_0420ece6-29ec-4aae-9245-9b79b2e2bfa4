package com.ruoyi.ffsafe.api.service;

import com.ruoyi.ffsafe.api.domain.FfsafeHostIntrusionAttackDetail;

import java.util.List;

/**
 * 主机入侵攻击详情Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
public interface IFfsafeHostIntrusionAttackDetailService {
    
    /**
     * 查询主机入侵攻击详情
     * 
     * @param id 主机入侵攻击详情主键
     * @return 主机入侵攻击详情
     */
    FfsafeHostIntrusionAttackDetail selectFfsafeHostIntrusionAttackDetailById(Long id);

    /**
     * 查询主机入侵攻击详情列表
     * 
     * @param ffsafeHostIntrusionAttackDetail 主机入侵攻击详情
     * @return 主机入侵攻击详情集合
     */
    List<FfsafeHostIntrusionAttackDetail> selectFfsafeHostIntrusionAttackDetailList(FfsafeHostIntrusionAttackDetail ffsafeHostIntrusionAttackDetail);

    /**
     * 根据攻击事件ID查询详情列表
     *
     * @param attackId 攻击事件ID
     * @return 详情列表
     */
    List<FfsafeHostIntrusionAttackDetail> selectByAttackId(Long attackId);

    /**
     * 根据攻击事件ID列表批量查询详情列表
     *
     * @param attackIds 攻击事件ID列表
     * @return 详情列表
     */
    List<FfsafeHostIntrusionAttackDetail> selectByAttackIds(List<Long> attackIds);

    /**
     * 新增主机入侵攻击详情
     * 
     * @param ffsafeHostIntrusionAttackDetail 主机入侵攻击详情
     * @return 结果
     */
    int insertFfsafeHostIntrusionAttackDetail(FfsafeHostIntrusionAttackDetail ffsafeHostIntrusionAttackDetail);

    /**
     * 修改主机入侵攻击详情
     * 
     * @param ffsafeHostIntrusionAttackDetail 主机入侵攻击详情
     * @return 结果
     */
    int updateFfsafeHostIntrusionAttackDetail(FfsafeHostIntrusionAttackDetail ffsafeHostIntrusionAttackDetail);

    /**
     * 批量删除主机入侵攻击详情
     * 
     * @param ids 需要删除的主机入侵攻击详情主键集合
     * @return 结果
     */
    int deleteFfsafeHostIntrusionAttackDetailByIds(Long[] ids);

    /**
     * 删除主机入侵攻击详情信息
     * 
     * @param id 主机入侵攻击详情主键
     * @return 结果
     */
    int deleteFfsafeHostIntrusionAttackDetailById(Long id);

    /**
     * 根据攻击事件ID删除详情
     * 
     * @param attackId 攻击事件ID
     * @return 结果
     */
    int deleteFfsafeHostIntrusionAttackDetailByAttackId(Long attackId);

    /**
     * 批量插入主机入侵攻击详情
     * 
     * @param list 主机入侵攻击详情列表
     * @return 结果
     */
    int batchInsertFfsafeHostIntrusionAttackDetail(List<FfsafeHostIntrusionAttackDetail> list);

    /**
     * 批量根据攻击事件ID删除详情
     * 
     * @param attackIds 攻击事件ID列表
     * @return 结果
     */
    int batchDeleteByAttackIds(List<Long> attackIds);
}
