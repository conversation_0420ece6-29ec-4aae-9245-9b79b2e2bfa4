package com.ruoyi.safe.controller;

import cn.anmte.aqsoc.asset.domain.TblAssetFieldsItem;
import cn.anmte.aqsoc.asset.service.ITblAssetFieldsItemService;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.annotation.DynamicsRequired;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.dict.domain.NetworkDomain;
import com.ruoyi.dict.domain.TblLocation;
import com.ruoyi.dict.service.INetworkDomainService;
import com.ruoyi.dict.service.ITblLocationService;
import com.ruoyi.safe.aspectj.AssetAction;
import com.ruoyi.safe.aspectj.AssetDataHandle;
import com.ruoyi.safe.countByDict.service.ICountByDictService;
import com.ruoyi.safe.domain.*;
import com.ruoyi.safe.domain.excel.TblNetworkDevicesJTExcel;
import com.ruoyi.safe.mapper.AssetTypeMapper;
import com.ruoyi.safe.mapper.TblVendorMapper;
import com.ruoyi.safe.service.*;
import com.ruoyi.safe.vo.countDict.CountDictTypeVO;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.common.core.domain.TreeSelect;
import com.ruoyi.safe.domain.dto.QueryDeptNetworkDevicesCountDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 网络设备Controller
 *
 * <AUTHOR>
 * @date 2022-11-08
 */
@RestController
@RequestMapping("/safe/networkdevices")
public class TblNetworkDevicesController extends BaseController
{
    @Autowired
    private ITblNetworkDevicesService tblNetworkDevicesService;

    @Autowired
    private ICountByDictService countByDictService;
    @Autowired
    private IMonitorHandleService monitorHandleService;
    @Autowired
    private ISysDeptService deptService;
    @Autowired
    private ISysDictDataService dictDataService;
    @Autowired
    private INetworkDomainService networkDomainService;
    @Autowired
    private ITblVendorService tblVendorService;
    @Autowired
    private Snowflake snowflake;
    @Autowired
    private ITblAssetOverviewService tblAssetOverviewService;
    @Autowired
    private IAssetVulnerabilityStatsService assetVulnerabilityStatsService;
    @Autowired
    private ITblLocationService tblLocationService;
    @Autowired
    private ITblBusinessApplicationService tblBusinessApplicationService;
    @Autowired
    private AssetTypeMapper assetTypeMapper;
    @Resource
    private ITblAssetFieldsItemService assetFieldsItemService;

    private static final String IPV4_REGEX =
            "^(?:(?:\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])\\.){3}(?:\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])$";
    /**
     * 查询网络设备列表
     */
    //@PreAuthorize("@ss.hasPermi('safe:networkdevices:list')")
    @DataScope(deptAlias = "a", userAlias = "a")
    @AssetDataHandle(action = AssetAction.SELECT)
    @GetMapping("/list")
    public TableDataInfo list(TblNetworkDevices tblNetworkDevices)
    {
        startPage();
        List<TblNetworkDevices> list = tblNetworkDevicesService.selectTblNetworkDevicesList(tblNetworkDevices);
        if (CollUtil.isNotEmpty( list)){
            List<JSONObject> assetFieldsItemList = tblBusinessApplicationService.selectAssetFieldsItemList("4");
            if (CollUtil.isNotEmpty(assetFieldsItemList)){
                List<JSONObject> basicInformationList = assetFieldsItemList.stream()
                        .filter(jsonObject -> "基本信息".equals(jsonObject.getString("formName"))).collect(Collectors.toList());
                for (int i = 0; i <list.size(); i++) {
                    TblNetworkDevices domain = list.get(i);
                    int denominator = assetFieldsItemList.size();
                    AtomicReference<Integer> numerator = new AtomicReference<>(0);
                    if (CollUtil.isNotEmpty(basicInformationList)){
                        for (JSONObject jsonObject : basicInformationList){
                            String fieldKey = jsonObject.getString("fieldKey");
                            Object fieldValue = ReflectUtil.getFieldValue(domain, fieldKey);
                            if (fieldValue != null && !"".equals(fieldValue)){
                                numerator.getAndSet(numerator.get() + 1);
                            }
                        }
                    }
                    double completeness = ((double) numerator.get() / denominator) * 100;
                    BigDecimal bd = new BigDecimal(completeness).setScale(2, RoundingMode.DOWN);
                    domain.setCompletenessStr(bd.toString());
                    domain.setCompleteness(bd);
                }
            }
        }

        // 为网络设备列表添加统计信息
        assetVulnerabilityStatsService.batchEnrichNetworkDevicesWithStats(list);

        return getDataTable(list);
    }

    /**
     * 导出网络设备列表
     */
    @PreAuthorize("@ss.hasPermi('safe:networkdevices:export')")
    @Log(title = "网络设备", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TblNetworkDevices tblNetworkDevices)
    {
        List<TblNetworkDevices> list = tblNetworkDevicesService.selectTblNetworkDevicesList(tblNetworkDevices);
        List<SysDept> sysDepts = deptService.selectDeptList(new SysDept());
        List<AssetClass> assetClasses = assetTypeMapper.selectedAssetTypeChildrenByIdAPid(2L);
        list.forEach(e -> {
            if (StringUtils.isNotBlank(e.getLaseScanState())) {
                e.setState(e.getLaseScanState());
            }
            if(e.getManageDeptId() != null){
                if (CollUtil.isNotEmpty(sysDepts)){
                    SysDept sysDept = sysDepts.stream().filter(dept -> dept.getDeptId().equals(e.getManageDeptId())).findFirst().orElse(null);
                    e.setManageDeptName(sysDept!=null ? sysDept.getDeptName() : "");
                }
            }
            if (e.getAssetType() != null){
                if (CollUtil.isNotEmpty(assetClasses)){
                    AssetClass assetClass = assetClasses.stream().filter(assetClassIte -> assetClassIte.getId().equals(e.getAssetType())).findFirst().orElse(null);
                    e.setAssetTypeName(assetClass!=null ? assetClass.getTypeName() : "");
                }
            }
        });
        ExcelUtil<TblNetworkDevices> util = new ExcelUtil<TblNetworkDevices>(TblNetworkDevices.class);
        util.exportExcel(response, list, "网络设备数据");
    }

    /**
     * 获取网络设备详细信息
     */
    @PreAuthorize("@ss.hasPermi('safe:networkdevices:query')")
    @GetMapping(value = "/{assetId}")
    public AjaxResult getInfo(@PathVariable("assetId") Long assetId)
    {
        return AjaxResult.success(tblNetworkDevicesService.selectTblNetworkDevicesByAssetId(assetId));
    }

    /**
     * 新增网络设备
     */
    @PreAuthorize("@ss.hasPermi('safe:networkdevices:add')")
    @Log(title = "网络设备", businessType = BusinessType.INSERT)
    @AssetDataHandle(action = AssetAction.INSERT)
    @PostMapping
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult add(@RequestBody TblNetworkDevices tblNetworkDevices)
    {
        if (StrUtil.isNotBlank(tblNetworkDevices.getIp())){
            List<String> ips = StrUtil.split(tblNetworkDevices.getIp(),",");
            monitorHandleService.deleteByIp(ips);
        }
        AjaxResult ajaxResult = toAjax(tblNetworkDevicesService.insertTblNetworkDevices(tblNetworkDevices));
        ajaxResult.put("data",tblNetworkDevices);
        return ajaxResult;
    }

    /**
     * 修改网络设备
     */
    @PreAuthorize("@ss.hasPermi('safe:networkdevices:edit')")
    @Log(title = "网络设备", businessType = BusinessType.UPDATE)
    @AssetDataHandle(action = AssetAction.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TblNetworkDevices tblNetworkDevices)
    {
        return toAjax(tblNetworkDevicesService.updateTblNetworkDevices(tblNetworkDevices));
    }

    /**
     * 删除网络设备
     */
    @PreAuthorize("@ss.hasPermi('safe:networkdevices:remove')")
    @Log(title = "网络设备", businessType = BusinessType.DELETE)
	@DeleteMapping("/{assetIds}")
    public AjaxResult remove(@PathVariable Long[] assetIds)
    {
        return toAjax(tblNetworkDevicesService.deleteTblNetworkDevicesByAssetIds(assetIds));
    }

    /**
     * 根据不同的字典类型统计网络设备数量
     * @param dictType
     * @return
     */
    @GetMapping("/getNetworkdevicesCountByDict")
    public AjaxResult getNetworkdevicesCountByDict(String dictType) {
        CountDictTypeVO countByDict = countByDictService.getCountByDict(dictType,"networkdevices");
        return AjaxResult.success(countByDict);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<TblNetworkDevicesTemlate> util = new ExcelUtil<TblNetworkDevicesTemlate>(TblNetworkDevicesTemlate.class);
        List<TblNetworkDevicesTemlate> list = new ArrayList<>();
        TblNetworkDevicesTemlate tblNetworkDevices = new TblNetworkDevicesTemlate();
        tblNetworkDevices.setAssetName("例如：XX网络设备");
        tblNetworkDevices.setIp("例如：***********");
        tblNetworkDevices.setAssetCode("例如：WLSB112323");
        tblNetworkDevices.setAssetTypeName("例如：XX类型");
        tblNetworkDevices.setDeptName("例如：XX部门");
        tblNetworkDevices.setDomainName("例如：XX网络区域");
        tblNetworkDevices.setDegreeImportance("例如：非常重要/重要/一般/不太重要/不重要");
        tblNetworkDevices.setIsVirtual("例如：是/否");
        tblNetworkDevices.setSystemVersion("例如：Windows10");
        tblNetworkDevices.setBrandModel("例如：XX厂家");
        tblNetworkDevices.setVendorName("例如：XX供应商");
        tblNetworkDevices.setVer("例如：XX型号");
        tblNetworkDevices.setLocationDetail("例如：江西南昌XXX");
        /*tblNetworkDevices.setLocationFullName("XX机房");*/
        tblNetworkDevices.setPurpose("例如：XX用途");
        tblNetworkDevices.setRemark("例如：XX备注");
        tblNetworkDevices.setManageDeptName("例如：XX部门");
        tblNetworkDevices.setManageAddress("例如：XX地址");
        tblNetworkDevices.setInterconnectVlan("例如:XXvlan");
        tblNetworkDevices.setInterconnectManage("例如：XX地址");
        tblNetworkDevices.setSwitchInterconnectManage("例如：XX地址");
        tblNetworkDevices.setIsSparing("例如：是/否");
        tblNetworkDevices.setUsageTime(new Date());
        list.add(tblNetworkDevices);
        util.exportExcel(response,list,"网络设备模板");
    }

    @PostMapping("/importTemplateJT")
    public void importTemplateJT(HttpServletResponse response) {
        ExcelUtil<TblNetworkDevicesJTExcel> util = new ExcelUtil<TblNetworkDevicesJTExcel>(TblNetworkDevicesJTExcel.class);
        List<TblNetworkDevicesJTExcel> list = new ArrayList<>();
        TblNetworkDevicesJTExcel tblNetworkDevices = new TblNetworkDevicesJTExcel();
        List<AssetClass> assetClasses = assetTypeMapper.selectedAssetTypeChildrenByIdAPid(2L);
        if(CollUtil.isNotEmpty(assetClasses)){
            tblNetworkDevices.setAssetType(StrUtil.format("例如：{}",CollUtil.join(assetClasses.stream().map(AssetClass::getTypeName).collect(Collectors.toList()), "/")));
        }
        tblNetworkDevices.setAssetName("例如：XX网络设备");
        tblNetworkDevices.setBrandModel("例如：XX厂家");
        tblNetworkDevices.setVer("例如：XX型号");
        tblNetworkDevices.setSystemVersion("例如：Windows10");
        tblNetworkDevices.setPurpose("例如：XX用途");
        tblNetworkDevices.setManageAddress("例如：XX地址");
        tblNetworkDevices.setInterconnectVlan("例如:XXvlan");
        tblNetworkDevices.setInterconnectManage("例如：XX地址");
        tblNetworkDevices.setSwitchInterconnectManage("例如：XX地址");
        tblNetworkDevices.setDomainName("例如：XX网络区域");
        tblNetworkDevices.setDeptName("例如：XX部门");
        tblNetworkDevices.setDegreeImportance("例如：非常重要/重要/一般/不太重要/不重要");
        tblNetworkDevices.setIsSparing("例如：是/否");
        tblNetworkDevices.setLocationId("例如：XX机房");
        tblNetworkDevices.setLocationFullName("例如：XX省XX市XX区XX机房");
        tblNetworkDevices.setIp("例如：***********");
        tblNetworkDevices.setUsageTime(new Date());
        tblNetworkDevices.setManageDeptId("例如：XX部门");
        tblNetworkDevices.setVendor("例如：XX供应商");
        tblNetworkDevices.setRemark("例如：XX备注");
        list.add(tblNetworkDevices);

        Class<?> aClass = tblNetworkDevices.getClass();
        DynamicsRequired dynamicsRequired = aClass.getDeclaredAnnotation(DynamicsRequired.class);
        if(dynamicsRequired != null){
            TblAssetFieldsItem assetFieldsItem = new TblAssetFieldsItem();
            assetFieldsItem.setAssetType(dynamicsRequired.type());
            util.fieldsList = assetFieldsItemService.selectItemList(assetFieldsItem);
        }

        util.exportExcel(response,list,"网络设备模板");
    }

    @PostMapping("/importData")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        if (file == null || file.isEmpty()) {
            return AjaxResult.error("文件不能为空");
        }
        if (file.getSize() > 10 * 1024 * 1024) { // 限制文件大小为10MB
            return AjaxResult.error("文件大小不能超过10MB");
        }

        ExcelUtil<TblNetworkDevicesTemlate> util = new ExcelUtil<>(TblNetworkDevicesTemlate.class);
        List<TblNetworkDevicesTemlate> list = util.importExcel(file.getInputStream());
        if (CollUtil.isEmpty(list)) {
            return AjaxResult.error("导入数据为空");
        }

        try {
            // 缓存部门、字典和网络区域信息
            Map<String, SysDept> deptMap = deptService.selectDeptList(new SysDept())
                    .stream().collect(Collectors.toMap(SysDept::getDeptName, dept -> dept));
            SysDictData dictData = new SysDictData();
            dictData.setDictType("impt_grade");
            Map<String, SysDictData> dictMap = dictDataService.selectDictDataList(dictData)
                    .stream().collect(Collectors.toMap(SysDictData::getDictLabel, data -> data));
            Map<String, NetworkDomain> domainMap = networkDomainService.selectNetworkDomainList(new NetworkDomain())
                    .stream().collect(Collectors.toMap(NetworkDomain::getDomainName, domain -> domain));
            Map<String, TblVendor> vendorMap = tblVendorService.selectTblVendorList(new TblVendor())
                    .stream().collect(Collectors.toMap(TblVendor::getVendorName, vendor -> vendor));
            Map<String, TblLocation> vendorLocationMap = tblLocationService.selectTblLocationList(new TblLocation())
                    .stream().collect(Collectors.toMap(TblLocation::getLocationFullName, location -> location));
            List<AssetClass> assetClasses = assetTypeMapper.selectedAssetTypeChildrenByIdAPid(2L);
            int index = 1; // 行号从1开始
            for (TblNetworkDevicesTemlate tblNetworkDevices : list) {
                // 校验字段
                checkField(tblNetworkDevices.getAssetName(), "资产名称", index);
                checkField(tblNetworkDevices.getIp(), "IP地址", index);
                checkField(tblNetworkDevices.getDeptName(), "所属部门", index);
                checkField(tblNetworkDevices.getDomainName(), "所属网络", index);

                // 校验IP地址
                if (!isValidIPv4(tblNetworkDevices.getIp())) {
                    throw new ServiceException("第" + index + "行, IP地址格式错误");
                }

                if (StrUtil.isNotBlank(tblNetworkDevices.getAssetTypeName())){
                    if (CollUtil.isNotEmpty(assetClasses)){
                        AssetClass aClass = assetClasses.stream().filter(assetClass -> assetClass.getTypeName()
                                .equals(tblNetworkDevices.getAssetType())).findFirst().orElse(null);
                        if (aClass != null){
                            tblNetworkDevices.setAssetType(aClass.getId());
                        }
                    }
                }

                // 自动生成资产编码
                if (StrUtil.isBlank(tblNetworkDevices.getAssetCode())){
                    tblNetworkDevices.setAssetCode("WLSB"+DateUtil.format(new Date(), "yyyyMMddHHmmss"+index));
                }

                // 校验供应商
                TblVendor tblVendor = vendorMap.get(tblNetworkDevices.getVendorName());
                if (tblVendor == null) {
                    /*throw new ServiceException("第" + index + "行, 供应商不存在");*/
                    tblNetworkDevices.setVendor(null);
                }else {
                    tblNetworkDevices.setVendor(tblVendor.getId());
                }

                //校验资产位置
                TblLocation tblLocation = vendorLocationMap.get(tblNetworkDevices.getLocationFullName());
                if (tblLocation == null) {
                    /*throw new ServiceException("第" + index + "行, 位置不存在");*/
                    tblNetworkDevices.setLocationId(null);
                }else{
                    tblNetworkDevices.setLocationId(tblLocation.getLocationId().toString());
                }

                //校验虚拟设备
                if (StrUtil.isNotBlank(tblNetworkDevices.getIsVirtual())){
                    if ("是".equals(tblNetworkDevices.getIsVirtual())){
                        tblNetworkDevices.setIsVirtual("Y");
                    }else if ("否".equals(tblNetworkDevices.getIsVirtual())){
                        tblNetworkDevices.setIsVirtual("N");
                    }else {
                        throw new ServiceException("第" + index + "行, 虚拟设备填写错误");
                    }
                }

                if(StrUtil.isNotBlank(tblNetworkDevices.getIsSparing())){
                    if ("是".equals(tblNetworkDevices.getIsSparing())){
                        tblNetworkDevices.setIsSparing("1");
                    }else if ("否".equals(tblNetworkDevices.getIsSparing())){
                        tblNetworkDevices.setIsSparing("0");
                    }
                }

                if (StrUtil.isNotBlank(tblNetworkDevices.getManageDeptName())){
                    SysDept manageDeptId = deptMap.get(tblNetworkDevices.getManageDeptName());
                    if (manageDeptId == null) {
                        tblNetworkDevices.setManageDeptId(null);
                    }else{
                        tblNetworkDevices.setManageDeptId(manageDeptId.getDeptId());
                    }
                }

                // 校验部门
                SysDept dept = deptMap.get(tblNetworkDevices.getDeptName());
                if (dept == null) {
                    throw new ServiceException("导入失败: 第" + index + "行 " + tblNetworkDevices.getDeptName() + " 部门不存在");
                }
                tblNetworkDevices.setDeptId(dept.getDeptId());

                // 校验网络区域
                NetworkDomain domain = domainMap.get(tblNetworkDevices.getDomainName());
                if (domain == null) {
                    throw new ServiceException("导入失败: 第" + index + "行 " + tblNetworkDevices.getDomainName() + " 网络区域不存在");
                }
                tblNetworkDevices.setDomainId(domain.getDomainId());

                // 校验重要程度
                if (StrUtil.isNotBlank(tblNetworkDevices.getDegreeImportance())) {
                    SysDictData dictDataFind = dictMap.get(tblNetworkDevices.getDegreeImportance());
                    if (dictDataFind == null) {
                        throw new ServiceException("导入失败: 第" + index + "行 " + tblNetworkDevices.getDegreeImportance() + " 重要程度不存在");
                    }
                    tblNetworkDevices.setDegreeImportance(dictDataFind.getDictValue());
                }
                //校验资产编码是否唯一
                TblAssetOverview tblAssetOverview = new TblAssetOverview();
                tblAssetOverview.setAssetCode(tblNetworkDevices.getAssetCode());
                if (com.ruoyi.common.utils.StringUtils.isNotEmpty(tblNetworkDevices.getAssetCode()) && !tblAssetOverviewService.checkAssetCodeUnique(tblAssetOverview)) {
                    throw new ServiceException("导入失败: 第" + index + "行 " + tblNetworkDevices.getAssetCode() + "资产编码已存在");
                }

                // 插入数据
                TblNetworkDevices insertTblNetworkDevices = new TblNetworkDevices();
                BeanUtils.copyProperties(tblNetworkDevices, insertTblNetworkDevices);
                insertTblNetworkDevices.setAssetId(snowflake.nextId());
                insertTblNetworkDevices.setCreateBy(getUsername());
                if (insertTblNetworkDevices.getUserId() == null) {
                    insertTblNetworkDevices.setUserId(getUserId());
                }
                if (insertTblNetworkDevices.getDeptId() == null) {
                    insertTblNetworkDevices.setDeptId(getDeptId());
                }
                if (tblNetworkDevicesService.insertTblNetworkDevices(insertTblNetworkDevices) <= 0) {
                    throw new ServiceException("导入失败: 第" + index + "行 " + tblNetworkDevices.getAssetName() + " 网络设备导入失败");
                }

                index++;
            }
        } catch (ServiceException e) {
            logger.error("导入数据出错: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("系统异常: {}", e.getMessage(), e);
            throw new ServiceException("数据导入错误: " + e.getMessage());
        }

        return AjaxResult.success("导入成功");
    }

    @PostMapping("/importDataJT")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult importDataJT(MultipartFile file, boolean updateSupport) throws Exception
    {
        if (file == null || file.isEmpty()) {
            return AjaxResult.error("文件不能为空");
        }
        if (file.getSize() > 10 * 1024 * 1024) { // 限制文件大小为10MB
            return AjaxResult.error("文件大小不能超过10MB");
        }

        try {
            ExcelUtil<TblNetworkDevicesJTExcel> util = new ExcelUtil<>(TblNetworkDevicesJTExcel.class);

            DynamicsRequired dynamicsRequired = util.clazz.getDeclaredAnnotation(DynamicsRequired.class);
            if(dynamicsRequired != null){
                TblAssetFieldsItem assetFieldsItem = new TblAssetFieldsItem();
                assetFieldsItem.setAssetType(dynamicsRequired.type());
                util.fieldsList = assetFieldsItemService.selectItemList(assetFieldsItem);
            }

            List<TblNetworkDevicesJTExcel> list = util.importExcel(file.getInputStream());
            if (CollUtil.isEmpty(list)) {
                return AjaxResult.error("导入数据为空");
            }
            // 缓存部门、字典和网络区域信息
            SysDept sysDept = new SysDept();
            sysDept.setQueryAllData(true);
            List<SysDept> deptList = deptService.selectDeptList(sysDept);
            SysDictData dictData = new SysDictData();
            dictData.setDictType("impt_grade");
            Map<String, SysDictData> dictMap = dictDataService.selectDictDataList(dictData)
                    .stream().collect(Collectors.toMap(SysDictData::getDictLabel, data -> data));
            Map<String, NetworkDomain> domainMap = networkDomainService.selectNetworkDomainList(new NetworkDomain())
                    .stream().collect(Collectors.toMap(NetworkDomain::getDomainName, domain -> domain));
            Map<String, TblVendor> vendorMap = tblVendorService.selectTblVendorList(new TblVendor())
                    .stream().collect(Collectors.toMap(TblVendor::getVendorName, vendor -> vendor));
            Map<String, TblLocation> vendorLocationMap = tblLocationService.selectTblLocationList(new TblLocation())
                    .stream().collect(Collectors.toMap(TblLocation::getLocationFullName, location -> location));
            List<AssetClass> assetClasses = assetTypeMapper.selectedAssetTypeChildrenByIdAPid(2L);
            int index = 1; // 行号从1开始
            for (TblNetworkDevicesJTExcel tblNetworkDevices : list) {
                TblNetworkDevices insert = new TblNetworkDevices();
                //资产类型
                String assetType = tblNetworkDevices.getAssetType();
                if(StrUtil.isNotBlank(assetType)){
                    AssetClass matchAssetClass = assetClasses.stream().filter(assetClass -> assetClass.getTypeName().equals(assetType)).findFirst().orElse(null);
                    if(matchAssetClass != null){
                        insert.setAssetType(matchAssetClass.getId());
                    }
                }
                //资产名称
                insert.setAssetName(tblNetworkDevices.getAssetName());
                //厂家/型号
                /*if(StrUtil.isNotBlank(tblNetworkDevices.getBrandAndModel())){
                    String brand = StrUtil.subBefore(tblNetworkDevices.getBrandAndModel(), " ", false);
                    String ver = StrUtil.subAfter(tblNetworkDevices.getBrandAndModel(), " ", false);
                    insert.setBrandModel(brand);
                    insert.setVer(ver);
                }*/
                if (StrUtil.isNotBlank(tblNetworkDevices.getBrandModel())){
                    insert.setBrandModel(tblNetworkDevices.getBrandModel());
                }
                if (StrUtil.isNotBlank(tblNetworkDevices.getVer())){
                    insert.setVer(tblNetworkDevices.getVer());
                }
                //系统及版本
                insert.setSystemVersion(tblNetworkDevices.getSystemVersion());
                //用途
                insert.setPurpose(tblNetworkDevices.getPurpose());
                //管理地址
                insert.setManageAddress(tblNetworkDevices.getManageAddress());
                //互联VLAN
                insert.setInterconnectVlan(tblNetworkDevices.getInterconnectVlan());
                //互联地址
                insert.setInterconnectManage(tblNetworkDevices.getInterconnectManage());
                //上互联地址
                insert.setSwitchInterconnectManage(tblNetworkDevices.getSwitchInterconnectManage());
                //所属网络区域
                if(StrUtil.isNotBlank(tblNetworkDevices.getDomainName())){
                    NetworkDomain matchNetworkDomain = domainMap.get(tblNetworkDevices.getDomainName());
                    if(matchNetworkDomain != null){
                        insert.setDomainId(matchNetworkDomain.getDomainId());
                    }
                }
                //IP
                insert.setIp(tblNetworkDevices.getIp());
                //所属部门
                if(StrUtil.isNotBlank(tblNetworkDevices.getDeptName())){
                    List<String> deptNameArr = StrUtil.split(tblNetworkDevices.getDeptName(), "-");
                    SysDept firstDept = deptList.stream().filter(dept -> dept.getDeptName().equals(deptNameArr.get(0))).findFirst().orElse(null);
                    if(firstDept != null){
                        List<SysDept> curDeptList = deptList.stream().filter(dept -> StrUtil.split(dept.getAncestors(), ",").contains(firstDept.getDeptId().toString())).collect(Collectors.toList());
                        Long curDeptId = firstDept.getDeptId();
                        for (int i = 1; i < deptNameArr.size(); i++) {
                            int finalI = i;
                            SysDept curDept = curDeptList.stream().filter(dept -> dept.getDeptName().equals(deptNameArr.get(finalI))).findFirst().orElse(null);
                            if(curDept != null){
                                curDeptId = curDept.getDeptId();
                            }
                        }
                        insert.setDeptId(curDeptId);
                    }
                }
                //重要程度
                insert.setDegreeImportance(tblNetworkDevices.getDegreeImportance());
                //是否热设备
                insert.setIsSparing(tblNetworkDevices.getIsSparing());
                if (StrUtil.isNotBlank(tblNetworkDevices.getLocationId()) && StrUtil.isNotBlank(tblNetworkDevices.getLocationFullName())){
                    TblLocation location = vendorLocationMap.get(tblNetworkDevices.getLocationFullName());
                    if(location != null){
                        if (location.getLocationName().equals(tblNetworkDevices.getLocationId())){
                            insert.setLocationId(location.getLocationId().toString());
                        }
                    }
                } else if (StrUtil.isNotBlank(tblNetworkDevices.getLocationId()) && !StrUtil.isNotBlank(tblNetworkDevices.getLocationFullName())) {
                    TblLocation location = vendorLocationMap.get(tblNetworkDevices.getLocationId());
                    if(location != null){
                        insert.setLocationId(location.getLocationId().toString());
                    }
                } else if (StrUtil.isNotBlank(tblNetworkDevices.getLocationFullName()) && !StrUtil.isNotBlank(tblNetworkDevices.getLocationId())) {
                    TblLocation location = vendorLocationMap.get(tblNetworkDevices.getLocationFullName());
                    if(location != null){
                        insert.setLocationId(location.getLocationId().toString());
                    }
                }
                //预计使用时间
                insert.setUsageTime(tblNetworkDevices.getUsageTime());
                //管理部门
                if(StrUtil.isNotBlank(tblNetworkDevices.getManageDeptId())){
                    List<String> deptNameArr = StrUtil.split(tblNetworkDevices.getManageDeptId(), "-");
                    SysDept firstDept = deptList.stream().filter(dept -> dept.getDeptName().equals(deptNameArr.get(0))).findFirst().orElse(null);
                    if(firstDept != null){
                        List<SysDept> curDeptList = deptList.stream().filter(dept -> StrUtil.split(dept.getAncestors(), ",").contains(firstDept.getDeptId().toString())).collect(Collectors.toList());
                        Long curDeptId = firstDept.getDeptId();
                        for (int i = 1; i < deptNameArr.size(); i++) {
                            int finalI = i;
                            SysDept curDept = curDeptList.stream().filter(dept -> dept.getDeptName().equals(deptNameArr.get(finalI))).findFirst().orElse(null);
                            if(curDept != null){
                                curDeptId = curDept.getDeptId();
                            }
                        }
                        insert.setManageDeptId(curDeptId);
                    }
                }
                //供应商
                if(StrUtil.isNotBlank(tblNetworkDevices.getVendor())){
                    TblVendor vendor = vendorMap.get(tblNetworkDevices.getVendor());
                    if(vendor == null){
                        //新增
                        vendor = new TblVendor();
                        vendor.setVendorName(tblNetworkDevices.getVendor());
                        vendor.setVendorPhone(tblNetworkDevices.getVendorPhone());
                        vendor.setCreateBy(getUsername());
                        vendor.setCreateTime(DateUtil.date());
                        vendor.setVendorCode("GYS"+DateUtil.format(new Date(), "yyyyMMddHHmmssSSS"+index));
                        vendor.setUserId(getUserId());
                        vendor.setDeptId(getDeptId());
                        tblVendorService.insertTblVendor(vendor);
                        vendorMap.put(tblNetworkDevices.getVendor(), vendor);
                        insert.setVendor(vendor.getId());
                    }else {
                        insert.setVendor(vendor.getId());
                    }
                }
                //备注
                insert.setRemark(tblNetworkDevices.getRemark());


                // 自动生成资产编码
                if (StrUtil.isBlank(insert.getAssetCode())){
                    insert.setAssetCode("WLSB"+DateUtil.format(new Date(), "yyyyMMddHHmmssSSS"+index));
                }

                // 插入数据
                insert.setAssetId(snowflake.nextId());
                insert.setCreateBy(getUsername());
                if (insert.getUserId() == null) {
                    insert.setUserId(getUserId());
                }
                if (insert.getDeptId() == null) {
                    insert.setDeptId(getDeptId());
                }
                if (tblNetworkDevicesService.insertTblNetworkDevices(insert) <= 0) {
                    throw new ServiceException("导入失败: 第" + index + "行 " + tblNetworkDevices.getAssetName() + " 网络设备导入失败");
                }

                index++;
            }
        } catch (ServiceException e) {
            logger.error("导入数据出错: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("系统异常: {}", e.getMessage(), e);
            throw new ServiceException("数据导入错误: " + e.getMessage());
        }

        return AjaxResult.success("导入成功");
    }

    private void checkField(String value, String fieldName,int index) throws Exception {
        if (StrUtil.isBlank(value)) {
            throw new Exception("导入失败:第"+index+"行,"+ fieldName + "不能为空");
        }
    }

    public static boolean isValidIPv4(String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }
        return ip.matches(IPV4_REGEX);
    }

    /**
     * 获取部门网络设备统计
     */
    @GetMapping("/getDepts")
    public AjaxResult getDepts() {
        QueryDeptNetworkDevicesCountDto queryCountDto = new QueryDeptNetworkDevicesCountDto();
        SysDept sysDept = new SysDept();
        sysDept.setDeptId(getDeptId());
        queryCountDto.setSysDept(sysDept);
        return AjaxResult.success(tblNetworkDevicesService.getDeptNetworkDevicesCount(queryCountDto));
    }

}
