# 修复 PullHostIntrusionAttackEvent 数据拉取逻辑缺陷

## 任务背景
当 `deviceConfig.getHostIntrusionLastTime()` 返回 null 时，当前代码逻辑存在缺陷：将 `lastDataTime` 设置为当前时间，导致 `getHostIntrusionAttackParam` 方法无法获取历史数据。

## 问题分析
**原始问题代码（第84-86行）：**
```java
if(deviceConfig.getHostIntrusionLastTime() == null){
    deviceConfig.setHostIntrusionLastTime(DateUtil.date()); // 设置为当前时间
}
```

**问题根因：**
1. 提前设置为当前时间，导致 `getHostIntrusionAttackParam` 方法永远不会进入 null 处理分支
2. 无法获取30天历史数据，只能从当前时间开始拉取
3. 逻辑重复，`getHostIntrusionAttackParam` 已有完善的 null 处理

## 修复方案
**采用方案1：** 移除提前设置逻辑，让 `getHostIntrusionAttackParam` 方法处理 null 值

**修改内容：**
- 删除第84-86行的 if 判断逻辑
- 直接传递原始的 `hostIntrusionLastTime` 值给 `FfsafeInterfaceConfig`
- 利用 `getHostIntrusionAttackParam` 方法现有的 null 处理逻辑（设置为30天前）

## 执行计划
1. **代码修改：** 删除 PullHostIntrusionAttackEvent.java 第84-86行
2. **逻辑验证：** 确认 getHostIntrusionAttackParam 的 null 处理正确
3. **影响分析：** 检查关联方法和数据库兼容性
4. **测试验证：** 验证新设备和已有设备的数据拉取

## 预期结果
- 新设备首次拉取将获取30天历史数据
- 现有设备功能不受影响
- 代码更简洁，职责分离更清晰
- 符合 Linus "好品味" 原则：消除特殊情况和重复逻辑

## 修改文件
- `aqsoc-monitor/src/main/java/com/ruoyi/ffsafe/api/event/PullHostIntrusionAttackEvent.java`

## 执行时间
2025-08-26

## 执行人
Linus Torvalds (Augment Agent)
