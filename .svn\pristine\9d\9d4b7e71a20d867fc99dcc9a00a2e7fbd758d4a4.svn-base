/**
 * 定时器混入，定时调用设备授权状态
 */

export default {
  data() {
    return {
      timer: null,
    };
  },
  methods: {
    startTimer() {
      this.timer = setInterval(() => {
        this.$store.dispatch('getAuthStatus').then(res => {})
      }, 1000 * 60 * 15); // 每15min调用一次
    },
    stopTimer() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    }
  },
  created() {
    this.startTimer(); // 组件创建时启动定时器
  },
  beforeDestroy() {
    this.stopTimer(); // 组件销毁前停止定时器
  }
};
