use aqsoc;

-- =====================================================
-- 数据迁移：从 ffsafe_scantask_summary 迁移报表数据
-- =====================================================
INSERT INTO ffsafe_scan_report_record (
    create_time,
    generate_source,
    report_type,
    report_id,
    report_status,
    file_name,
    down_name,
    report_percent,
    minio_path
)
SELECT
    NOW() as create_time,
    1 as generate_source,
    s.task_type as report_type,
    s.report_id,
    s.report_status,
    s.file_name,
    s.down_name,
    s.report_percent,
    s.minio_path
FROM ffsafe_scantask_summary s
LEFT JOIN sys_job j ON s.job_id = j.job_id
WHERE s.report_id IS NOT NULL;


-- =====================================================
-- 迁移关联表数据
-- =====================================================

-- 插入报告与任务的关联关系到关联表
INSERT INTO ffsafe_scan_report_task_relation (
    scan_report_record_id,
    task_summary_id,
    create_time
)
SELECT
    r.id as scan_report_record_id,
    s.id as task_summary_id,
    NOW() as create_time
FROM ffsafe_scan_report_record r
INNER JOIN ffsafe_scantask_summary s ON s.report_id = r.report_id
WHERE r.generate_source = 1  -- 只处理单条生成的记录
AND s.report_id IS NOT NULL;