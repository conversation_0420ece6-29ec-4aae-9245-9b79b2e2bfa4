package com.ruoyi.ffsafe.api.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 主机入侵攻击处置请求DTO
 * 用于单个攻击事件的处置操作
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@Schema(name = "FfsafeHostIntrusionAttackHandleDto", description = "主机入侵攻击处置请求")
public class FfsafeHostIntrusionAttackHandleDto implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 攻击事件ID */
    @NotNull(message = "攻击事件ID不能为空")
    @Schema(description = "攻击事件ID", required = true)
    private Long id;
    
    /** 处置状态 */
    @NotNull(message = "处置状态不能为空")
    @Schema(description = "处置状态，0=未处置,1=已处置,2=忽略", required = true)
    private Integer handleState;
    
    /** 处置描述 */
    @Size(max = 255, message = "处置描述长度不能超过255个字符")
    @Schema(description = "处置描述")
    private String handleDesc;
}
