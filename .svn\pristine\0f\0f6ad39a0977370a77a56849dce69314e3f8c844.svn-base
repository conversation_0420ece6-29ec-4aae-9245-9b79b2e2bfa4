package cn.anmte.aqsoc.work.service.impl;

import cn.anmte.aqsoc.work.domain.WorkHwTask;
import cn.anmte.aqsoc.work.mapper.WorkHwTaskMapper;
import cn.anmte.aqsoc.work.service.IWorkHwTaskService;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.service.ISysDictDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @project 2.5.3_dev
 * @description:
 * @date 2025-08-25 16:47
 */
@Service
public class WorkHwTaskServiceImpl extends ServiceImpl<WorkHwTaskMapper, WorkHwTask> implements IWorkHwTaskService {
    @Resource
    private WorkHwTaskMapper workHwTaskMapper;
    @Resource
    private ISysDictDataService sysDictDataService;

    /**
     * 查询HW事务任务
     *
     * @param id HW事务任务主键
     * @return HW事务任务
     */
    @Override
    public WorkHwTask selectWorkHwTaskById(Long id)
    {
        return workHwTaskMapper.selectWorkHwTaskById(id);
    }

    /**
     * 批量查询HW事务任务
     *
     * @param ids HW事务任务主键集合
     * @return HW事务任务集合
     */
    @Override
    public List<WorkHwTask> selectWorkHwTaskByIds(Long[] ids)
    {
        return workHwTaskMapper.selectWorkHwTaskByIds(ids);
    }

    /**
     * 查询HW事务任务列表
     *
     * @param workHwTask HW事务任务
     * @return HW事务任务
     */
    @Override
    public List<WorkHwTask> selectWorkHwTaskList(WorkHwTask workHwTask)
    {
        return workHwTaskMapper.selectWorkHwTaskList(workHwTask);
    }

    /**
     * 新增HW事务任务
     *
     * @param workHwTask HW事务任务
     * @return 结果
     */
    @Override
    public int insertWorkHwTask(WorkHwTask workHwTask)
    {
        workHwTask.setCreateTime(DateUtils.getNowDate());
        return workHwTaskMapper.insertWorkHwTask(workHwTask);
    }

    /**
     * 修改HW事务任务
     *
     * @param workHwTask HW事务任务
     * @return 结果
     */
    @Override
    public int updateWorkHwTask(WorkHwTask workHwTask)
    {
        workHwTask.setUpdateTime(DateUtils.getNowDate());
        return workHwTaskMapper.updateWorkHwTask(workHwTask);
    }

    /**
     * 删除HW事务任务信息
     *
     * @param id HW事务任务主键
     * @return 结果
     */
    @Override
    public int deleteWorkHwTaskById(Long id)
    {
        return workHwTaskMapper.deleteWorkHwTaskById(id);
    }

    /**
     * 批量删除HW事务任务
     *
     * @param ids 需要删除的HW事务任务主键
     * @return 结果
     */
    @Override
    public int deleteWorkHwTaskByIds(Long[] ids)
    {
        return workHwTaskMapper.deleteWorkHwTaskByIds(ids);
    }

    /**
     * 获取阶段树
     * @param workHwTask
     * @return
     */
    @Override
    public List<JSONObject> getStageTree(WorkHwTask workHwTask) {
        List<JSONObject> result = new ArrayList<>();
        //获取阶段字典
        SysDictData queryDictData = new SysDictData();
        queryDictData.setDictType("hw_stage_class");
        List<SysDictData> dictDataList = sysDictDataService.selectDictDataList(queryDictData);
        if(CollUtil.isNotEmpty(dictDataList)){
            //按阶段统计
            List<WorkHwTask> taskList = baseMapper.selectWorkHwTaskList(workHwTask);
            dictDataList.forEach(dictData -> {
                JSONObject resultItem = new JSONObject();
                resultItem.put("value", dictData.getDictValue());
                resultItem.put("label", dictData.getDictLabel());
                resultItem.put("sort", dictData.getDictSort());
                resultItem.put("count", taskList.stream().filter(task -> task.getStageClass().equals(dictData.getDictValue())).count());
                result.add(resultItem);
            });
        }
        return result;
    }
}
