package com.ruoyi.ffsafe.api.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.ruoyi.common.utils.DateUtils;
import lombok.Data;

import java.util.List;

/**
 * 主机入侵攻击分页接口响应结果类
 * 
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HostIntrusionAttackResult {
    
    /** 返回消息 */
    @JsonProperty("msg")
    private String msg;
    
    /** 返回数据 */
    @JsonProperty("data")
    private List<HostIntrusionAttackItem> data;

    /** 数据总数 */
    @JsonProperty("total")
    private Integer total;

    /**
     * 主机入侵攻击事件数据项
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class HostIntrusionAttackItem {
        
        /** 数据ID */
        @JsonProperty("id")
        private Integer id;
        
        /** 攻击源IP */
        @JsonProperty("sip")
        private String sip;
        
        /** 目的IP */
        @JsonProperty("dip")
        private String dip;
        
        /** 目标IP主机名 */
        @JsonProperty("dip_name")
        private String dipName;
        
        /** 告警名称 */
        @JsonProperty("alert_name")
        private String alertName;
        
        /** 开始时间 */
        @JsonProperty("start_time")
        private String startTime;
        
        /** 最近告警时间 */
        @JsonProperty("update_time")
        private String updateTime;
        
        /** 处置状态 */
        @JsonProperty("handle_status_text")
        private String handleStatusText;
        
        /**
         * 转换为实体对象
         */
        public FfsafeHostIntrusionAttack toEntity() {
            FfsafeHostIntrusionAttack entity = new FfsafeHostIntrusionAttack();
            entity.setFfId(this.id);
            entity.setSip(this.sip);
            entity.setDip(this.dip);
            entity.setDipName(this.dipName);
            entity.setAlertName(this.alertName);
            entity.setStartTime(DateUtils.parseDate(this.startTime));
            entity.setUpdateTime(DateUtils.parseDate(this.updateTime));
            
            // 设置处置状态，默认为未处置
            entity.setHandleStateByText(this.handleStatusText != null ? this.handleStatusText : "未处置");
            
            return entity;
        }
    }
}
