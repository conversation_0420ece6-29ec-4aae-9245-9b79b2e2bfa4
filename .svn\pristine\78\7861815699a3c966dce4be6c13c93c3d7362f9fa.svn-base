package com.ruoyi.safe.domain.excel;

import com.ruoyi.common.annotation.DynamicsRequired;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 江铜网络设备导入模板
 * <AUTHOR>
 * 2025/08/11 16:25
 */
@Data
@DynamicsRequired(type = 4)
public class TblNetworkDevicesJTExcel implements Serializable {
    private static final long serialVersionUID = 1L;

    @Excel(name = "资产类型", refName = "assetType")
    private String assetType;

    @Excel(name = "资产名称", refName = "assetName")
    private String assetName;

    /*@Excel(name = "厂家/型号", refName = "brandModel")
    private String brandAndModel;*/

    @Excel(name = "设备厂家",refName = "brandModel")
    private String brandModel;

    @Excel(name = "设备型号",refName = "ver")
    private String ver;

    @Excel(name = "系统及版本", refName = "systemVersion")
    private String systemVersion;

    @Excel(name = "用途", refName = "purpose")
    private String purpose;

    @Excel(name = "管理地址", refName = "manageAddress")
    private String manageAddress;

    @Excel(name = "互联VLAN", refName = "interconnectVlan")
    private String interconnectVlan;

    @Excel(name = "互联地址", refName = "interconnectManage")
    private String interconnectManage;

    @Excel(name = "上互联地址", refName = "switchInterconnectManage")
    private String switchInterconnectManage;

    @Excel(name = "所属网络区域", refName = "domainId")
    private String domainName;

    @Excel(name = "主IP", refName = "ip")
    private String ip;

    @Excel(name = "所属部门", refName = "deptId")
    private String deptName;

    @Excel(name = "重要程度", refName = "degreeImportance", dictType = "impt_grade")
    private String degreeImportance;

    @Excel(name = "是否热设备", refName = "isSparing", dictType = "is_sparing")
    private String isSparing;

    @Excel(name = "所在位置", refName = "locationId")
    private String locationId;

    @Excel(name = "详细位置")
    private String locationFullName;

    @Excel(name = "预计使用时间", refName = "usageTime", width = 30, dateFormat = "yyyy/M/d")
    private Date usageTime;

    @Excel(name = "管理部门", refName = "manageDeptId")
    private String manageDeptId;

    @Excel(name = "供应商", refName = "vendor")
    private String vendor;

    @Excel(name = "供应商电话", refName = "vendorPhone")
    private String vendorPhone;

    @Excel(name = "备注", refName = "remark")
    private String remark;
}
