{"remainingRequest": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\mixins\\generator\\timerMixin.js", "dependencies": [{"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\src\\mixins\\generator\\timerMixin.js", "mtime": 1756199950504}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\babel.config.js", "mtime": 1751890080904}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751956513812}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751956526634}, {"path": "E:\\wsh\\augment_workspace\\aqsoc-main\\ruoyi-ui\\node_modules\\eslint-loader\\index.js", "mtime": 1751956515056}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7Ci8qKgogKiDlrprml7blmajmt7flhaXvvIzlrprml7bosIPnlKjorr7lpIfmjojmnYPnirbmgIEKICovCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgdGltZXI6IG51bGwKICAgIH07CiAgfSwKICBtZXRob2RzOiB7CiAgICBzdGFydFRpbWVyOiBmdW5jdGlvbiBzdGFydFRpbWVyKCkgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgICB0aGlzLnRpbWVyID0gc2V0SW50ZXJ2YWwoZnVuY3Rpb24gKCkgewogICAgICAgIGNvbnNvbGUubG9nKCdUaGlzIGlzIGEgZ2xvYmFsIHRpbWVyIGNhbGwnKTsKICAgICAgICBfdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ2dldEF1dGhTdGF0dXMnKS50aGVuKGZ1bmN0aW9uIChyZXMpIHt9KTsKICAgICAgfSwgMTAwMCAqIDYwICogMTUpOyAvLyDmr48xNW1pbuiwg+eUqOS4gOasoQogICAgfSwKICAgIHN0b3BUaW1lcjogZnVuY3Rpb24gc3RvcFRpbWVyKCkgewogICAgICBpZiAodGhpcy50aW1lcikgewogICAgICAgIGNsZWFySW50ZXJ2YWwodGhpcy50aW1lcik7CiAgICAgICAgdGhpcy50aW1lciA9IG51bGw7CiAgICAgIH0KICAgIH0KICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB0aGlzLnN0YXJ0VGltZXIoKTsgLy8g57uE5Lu25Yib5bu65pe25ZCv5Yqo5a6a5pe25ZmoCiAgfSwKICBiZWZvcmVEZXN0cm95OiBmdW5jdGlvbiBiZWZvcmVEZXN0cm95KCkgewogICAgdGhpcy5zdG9wVGltZXIoKTsgLy8g57uE5Lu26ZSA5q+B5YmN5YGc5q2i5a6a5pe25ZmoCiAgfQp9Ow=="}, {"version": 3, "names": ["_default", "exports", "default", "data", "timer", "methods", "startTimer", "_this", "setInterval", "console", "log", "$store", "dispatch", "then", "res", "stopTimer", "clearInterval", "created", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["E:/wsh/augment_workspace/aqsoc-main/ruoyi-ui/src/mixins/generator/timerMixin.js"], "sourcesContent": ["/**\n * 定时器混入，定时调用设备授权状态\n */\n\nexport default {\n  data() {\n    return {\n      timer: null,\n    };\n  },\n  methods: {\n    startTimer() {\n      this.timer = setInterval(() => {\n        console.log('This is a global timer call');\n        this.$store.dispatch('getAuthStatus').then(res => {})\n      }, 1000 * 60 * 15); // 每15min调用一次\n    },\n    stopTimer() {\n      if (this.timer) {\n        clearInterval(this.timer);\n        this.timer = null;\n      }\n    }\n  },\n  created() {\n    this.startTimer(); // 组件创建时启动定时器\n  },\n  beforeDestroy() {\n    this.stopTimer(); // 组件销毁前停止定时器\n  }\n};\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AAFA,IAAAA,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAIe;EACbC,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,KAAK,EAAE;IACT,CAAC;EACH,CAAC;EACDC,OAAO,EAAE;IACPC,UAAU,WAAVA,UAAUA,CAAA,EAAG;MAAA,IAAAC,KAAA;MACX,IAAI,CAACH,KAAK,GAAGI,WAAW,CAAC,YAAM;QAC7BC,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC1CH,KAAI,CAACI,MAAM,CAACC,QAAQ,CAAC,eAAe,CAAC,CAACC,IAAI,CAAC,UAAAC,GAAG,EAAI,CAAC,CAAC,CAAC;MACvD,CAAC,EAAE,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IACtB,CAAC;IACDC,SAAS,WAATA,SAASA,CAAA,EAAG;MACV,IAAI,IAAI,CAACX,KAAK,EAAE;QACdY,aAAa,CAAC,IAAI,CAACZ,KAAK,CAAC;QACzB,IAAI,CAACA,KAAK,GAAG,IAAI;MACnB;IACF;EACF,CAAC;EACDa,OAAO,WAAPA,OAAOA,CAAA,EAAG;IACR,IAAI,CAACX,UAAU,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;EACDY,aAAa,WAAbA,aAAaA,CAAA,EAAG;IACd,IAAI,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC;EACpB;AACF,CAAC", "ignoreList": []}]}