import request from '@/utils/request'

// 查询HW事务任务列表
export function listWorkHwTask(query) {
  return request({
    url: '/workHwTask/list',
    method: 'get',
    params: query
  })
}

// 查询HW事务任务详细
export function getWorkHwTask(id) {
  return request({
    url: '/workHwTask/' + id,
    method: 'get'
  })
}

// 新增HW事务任务
export function addWorkHwTask(data) {
  return request({
    url: '/workHwTask',
    method: 'post',
    data: data
  })
}

// 修改HW事务任务
export function updateWorkHwTask(data) {
  return request({
    url: '/workHwTask',
    method: 'put',
    data: data
  })
}

// 删除HW事务任务
export function delWorkHwTask(id) {
  return request({
    url: '/workHwTask/' + id,
    method: 'delete'
  })
}

// 查询阶段树
export function getStageTree(query) {
  return request({
    url: '/workHwTask/getStageTree',
    method: 'get',
    params: query
  })
}
