# 项目相关配置
ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: 3.8.4
  # 版权年份
  copyrightYear: 2022
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: D:/ruoyi/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math
  # modelfile path
  modalpath: D:/temp

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.ruoyi: debug
    com.ruoyi.monitor2.mapper: debug
    cn.anmte: debug
    org.springframework: warn
    com.deepoove.poi: warn
    springfox.documentation: error
    org.apache.http: warn
    root: info

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: druid
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size:  50MB
      # 设置总上传的文件大小
      max-request-size:  60MB
  # activiti 模块
  activiti:
    # 为true 自动创建相关表 true, drop-create
    database-schema-update: true
    # activti是否自动部署
    check-process-definitions: false
    # 打开历史操作记录
    db-history-used: true
    history-level: full
    # 解决SpringAutoDeployment 自动记录问题
    deployment-mode: never-fail
    #是否使用activti自带的用户体系
    db-identity-used: false
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 地址
    # host: **************
    host: ***************
    #host: 127.0.0.1
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # 密码
    password: hyite@2024
    # password:
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

  # ===================== RabbitMQ配置 =====================
  # RabbitMQ集群配置
  rabbitmq:
    # 集群节点配置
    addresses: ***************:5672,***************:5673,***************:5674
    username: admin
    password: Hyite@2024
    virtual-host: /wsh
    # 连接超时配置
    connection-timeout: 15000
    # 生产者重试策略
    template:
      retry:
        enabled: true
        initial-interval: 1000ms
        max-attempts: 3
        max-interval: 10000ms
        multiplier: 1.5
    # 发布确认配置
    publisher-confirm-type: correlated
    publisher-returns: true
    # 消费者监听器配置
    listener:
      simple:
        acknowledge-mode: manual
        prefetch: 1
        concurrency: 3
        max-concurrency: 10

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 30

# MyBatis配置
mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.**.domain,com.ruoyi.monitor2.host.entity,cn.anmte.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

#监控相关
monitor:
  # 启用开关
  enabled: false
  #所属网络的ID编号，一个网段一个服务器程序
  netid : A001
  #与agent对接的token
  wgToken: wgcloud
  #数据表监控间隔,单位毫秒，默认60分钟
  dbTableTimes: 3600000
  #服务接口监控间隔，单位毫秒，默认10分钟
  heathTimes: 600000

# Minio文件服务器配置
minio:
  # 文件访问地址(代理地址)
  server: https://***************
  outPort: 9000
  # 文件访问地址
  url: https://***************
  # 端口号
  port: 9000
  # 用户名，默认为minioadmin
  username: aqsoc
  # 密码，默认为minioadmin
  password: hyite@2024
  # 桶名
  bucketName: aqsoc

#文件服务器代理地址加桶名
localDomain:
  name: http://***************:9000/aqsoc

# JNPF接口代理配置
jnpf:
  proxy:
    servlet_url: /proxy/*
    target_url: http://***************:30001
#    target_url: http://127.0.0.1:30000

 # 短信通知平台配置
message:
  ip: http://**********:8088
  path: /sms-center/api/sms/openApi/v1/sendSms
  tokenPath: /sms-center/api/sms/openApi/v1/refreshToken
  clientKey: sms_18D6787E4E0
  clientId: client_2507277578134356989
  clientSecret: 0A26E39BD464886B747383F64E5BFE4A
  busCategory: 99999999
  busApp: 安全运营平台
  busAppCode: A_2506313671049741309
  busUnitNo: 360000310800
  busUnitNoName: 江西省公安厅科技信息化总队安全保障科
  isEnabled: true

# 配置的路径必须是存在的盘符 isTrue 是否启用
file:
  listen:
    path: F:\IdealworkSpace\TestDome\file
    backPath: F:\IdealworkSpace\TestDome\backFile
    isTrue: false

# 区分不同客户 fair：公、copper：同、manage：理
deployment:
  location: manage

# ffmepg相关
ffmpeg:
  path: D:\develop\ffmpeg-7.0-full_build\bin # ffmpeg路径
  hlsPath: D:\data # hls文件存放路径
  hls-prefix: hlsResource # hls文件访问前缀

#导出WORD文件配置
wordFile:
  # 模板路径
  templatePath: E:\templates
  #部署上线时需提前创建好
  tblWorkUrl: E:/exportWord/
  export:
    tblWorkDocName: 工单

cloudWalker:
  url: https://192.168.200.179/rpc
  token: d7FEbLietMUx8dS8zLea

# ffsafe:
#  url: https://192.168.200.201:23000
#  token: lst2BAzxJTQL0eSZ

springdoc:
  api-docs:
    enabled: true

# 图幻配置
tuhuan:
  url: https://192.168.200.55
  key: PROC0CB0E5176B3CAD5A1A49608D0FA5A27

# ===================== RabbitMQ交换机、路由、队列、加密配置 =====================
aqsoc:
  # RabbitMQ自定义配置
  rabbitmq:
    # 交换机配置
    exchange:
      # 数据同步交换机
      data-sync: purple-platform.data-sync
      # 设备交换机
      device: service-platform.device
    # 队列配置
    queue:
      # 数据同步队列
      data-sync: service-platform.data-sync
      # 设备前缀
      device-prefix: service-platform.device
    # 路由键配置
    routing-key:
      # 数据同步路由键
      data-sync: data-sync
      # 设备路由键前缀
      device-prefix: device
    # 加密配置
    encryption:
      # 是否启用消息加密，true开启，false关闭
      enabled: true
      # 加密密钥，必须是16位
      key: JNPF2024SECUREKY
      # 初始向量，必须是16位
      iv: JNPFINITV2024088
    sync:
      # 控制RabbitMQ同步功能的总开关
      enabled: true
