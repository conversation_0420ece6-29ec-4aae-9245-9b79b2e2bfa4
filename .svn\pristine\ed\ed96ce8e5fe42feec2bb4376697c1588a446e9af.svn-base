package com.ruoyi.ffsafe.api.domain;

import lombok.EqualsAndHashCode;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * 设备接入配置对象 tbl_device_config
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TblDeviceConfig extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 设备名称 */
    @Excel(name = "设备名称")
    private String deviceName;

    /** 设备IP */
    @Excel(name = "设备IP")
    private String deviceIp;

    /** 设备接入参数 */
    @Excel(name = "设备接入参数")
    private String deviceParams;

    /** 状态 */
    @Excel(name = "状态")
    private Integer status;

    /** 封禁日志最后更新时间 */
    private Date filterLogLastTime;

    /** 流量告警日志最后更新时间 */
    private Date alarmDetailLastTime;

    /** 流量风险资产最后更新时间 */
    private Date riskAssetLastTime;

    /** 主机入侵攻击最后更新时间 */
    private Date hostIntrusionLastTime;
}
