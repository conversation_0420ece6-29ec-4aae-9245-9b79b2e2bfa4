/**
 * 验证Java对象引用机制 - 证明ID回填逻辑正确性
 * 
 * 这个示例证明了当我们将对象添加到不同列表时，
 * 修改其中一个列表中对象的属性会影响到所有引用该对象的列表
 */
import java.util.*;

public class ObjectReferenceTest {
    
    static class TestEntity {
        private Long id;
        private String name;
        
        public TestEntity(String name) {
            this.name = name;
        }
        
        // getters and setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        @Override
        public String toString() {
            return "TestEntity{id=" + id + ", name='" + name + "'}";
        }
    }
    
    public static void main(String[] args) {
        // 模拟 FfsafeClientService 的逻辑
        
        // 1. 创建原始数据列表（模拟 batchEntityList）
        TestEntity entity1 = new TestEntity("entity1");
        TestEntity entity2 = new TestEntity("entity2");
        TestEntity entity3 = new TestEntity("entity3");
        
        List<TestEntity> batchEntityList = Arrays.asList(entity1, entity2, entity3);
        
        System.out.println("=== 初始状态 ===");
        System.out.println("batchEntityList: " + batchEntityList);
        
        // 2. 分离插入和更新列表（模拟 FfsafeClientService 的分离逻辑）
        List<TestEntity> insertList = new ArrayList<>();
        List<TestEntity> updateList = new ArrayList<>();
        
        for (TestEntity entity : batchEntityList) {
            // 模拟条件判断：假设 entity1 和 entity3 需要插入，entity2 需要更新
            if ("entity2".equals(entity.getName())) {
                entity.setId(999L); // 模拟已存在的ID
                updateList.add(entity);
            } else {
                insertList.add(entity); // 关键：添加的是同一个对象引用
            }
        }
        
        System.out.println("\n=== 分离后 ===");
        System.out.println("insertList: " + insertList);
        System.out.println("updateList: " + updateList);
        System.out.println("batchEntityList: " + batchEntityList);
        
        // 3. 模拟 MyBatis 批量插入后的ID回填
        System.out.println("\n=== 模拟ID回填 ===");
        for (int i = 0; i < insertList.size(); i++) {
            Long generatedId = 100L + i; // 模拟数据库生成的ID
            insertList.get(i).setId(generatedId);
            System.out.println("回填ID " + generatedId + " 到 insertList[" + i + "]");
        }
        
        // 4. 验证结果
        System.out.println("\n=== 验证结果 ===");
        System.out.println("insertList: " + insertList);
        System.out.println("updateList: " + updateList);
        System.out.println("batchEntityList: " + batchEntityList);
        
        // 5. 关键验证：检查对象引用是否相同
        System.out.println("\n=== 对象引用验证 ===");
        System.out.println("insertList[0] == batchEntityList[0]: " + 
            (insertList.get(0) == batchEntityList.get(0)));
        System.out.println("insertList[1] == batchEntityList[2]: " + 
            (insertList.get(1) == batchEntityList.get(2)));
        System.out.println("updateList[0] == batchEntityList[1]: " + 
            (updateList.get(0) == batchEntityList.get(1)));
        
        // 6. 结论
        System.out.println("\n=== 结论 ===");
        System.out.println("✅ 证明：对 insertList 中对象的ID回填会同时影响 batchEntityList");
        System.out.println("✅ 因此 sqlSession.flushStatements() 确实会回填ID到 batchEntityList");
    }
}

/*
预期输出：
=== 初始状态 ===
batchEntityList: [TestEntity{id=null, name='entity1'}, TestEntity{id=null, name='entity2'}, TestEntity{id=null, name='entity3'}]

=== 分离后 ===
insertList: [TestEntity{id=null, name='entity1'}, TestEntity{id=null, name='entity3'}]
updateList: [TestEntity{id=999, name='entity2'}]
batchEntityList: [TestEntity{id=null, name='entity1'}, TestEntity{id=999, name='entity2'}, TestEntity{id=null, name='entity3'}]

=== 模拟ID回填 ===
回填ID 100 到 insertList[0]
回填ID 101 到 insertList[1]

=== 验证结果 ===
insertList: [TestEntity{id=100, name='entity1'}, TestEntity{id=101, name='entity3'}]
updateList: [TestEntity{id=999, name='entity2'}]
batchEntityList: [TestEntity{id=100, name='entity1'}, TestEntity{id=999, name='entity2'}, TestEntity{id=101, name='entity3'}]

=== 对象引用验证 ===
insertList[0] == batchEntityList[0]: true
insertList[1] == batchEntityList[2]: true
updateList[0] == batchEntityList[1]: true

=== 结论 ===
✅ 证明：对 insertList 中对象的ID回填会同时影响 batchEntityList
✅ 因此 sqlSession.flushStatements() 确实会回填ID到 batchEntityList
*/
